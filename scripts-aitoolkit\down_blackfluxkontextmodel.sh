#!/bin/bash

# FLUX.1-Kontext-dev模型下载脚本
# 集成磁盘空间检测、垃圾文件清理、模型下载和完整性验证

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查磁盘总空间
check_disk_space() {
    log_info "正在检查磁盘总空间..."

    local total_space_kb=$(df /root/autodl-tmp/ --output=size | tail -n 1)
    local total_space_gb=$((total_space_kb / 1024 / 1024))

    log_info "当前磁盘总空间: ${total_space_gb}GB"

    if [ $total_space_gb -lt 58 ]; then
        log_error "磁盘总空间不足！"
        log_error "当前总空间: ${total_space_gb}GB"
        log_error "最少需要: 58GB"
        log_error "请扩充数据盘空间后再试"
        exit 1
    fi

    log_success "磁盘空间充足 (${total_space_gb}GB >= 58GB)"
}

# 清理垃圾文件
clean_cache() {
    log_info "正在清理数据盘垃圾文件..."

    local cleaned_size=0
    local temp_file="/tmp/cleanup_size.tmp"

    # 清理.downloading文件和其他临时文件
    find /root/autodl-tmp/ \( \
        -name '*.tmp' -o -name '*.part' -o -name '*.download' -o -name '*.downloading' -o \
        -name '.git*' -o -name '.*~' -o -name '*~' -o \
        -name '.DS_Store' -o -name '*.bak' -o -name '*.old' -o \
        -name '*.swp' -o -name '*.lock' -o -name '*.temp' -o \
        -name '*.log' -o -name '*.pid' -o -name 'core.*' -o \
        -name '*.incomplete' -o -name '*.partial' \
    \) -type f 2>/dev/null | while read -r file; do
        if [ -f "$file" ]; then
            local size=$(stat -c%s "$file" 2>/dev/null || echo 0)
            echo $((size / 1024 / 1024)) >> "$temp_file"
            rm -f "$file" 2>/dev/null
        fi
    done

    # 清理缓存目录
    find /root/autodl-tmp/ -type d \( \
        -name '.cache' -o -name 'cache' -o -name 'tmp' -o -name '.tmp' -o \
        -name 'huggingface' -o -name 'hub' -o -name '.huggingface' -o \
        -name '.Trash-*' -o -name '.trash' -o -name 'Trash' \
    \) 2>/dev/null | while read -r dir; do
        if [ -d "$dir" ]; then
            local size=$(du -sm "$dir" 2>/dev/null | cut -f1 || echo 0)
            echo $size >> "$temp_file"
            rm -rf "$dir" 2>/dev/null
        fi
    done

    # 清理空目录
    for i in {1..3}; do
        find /root/autodl-tmp/ -type d -empty -not -path '/root/autodl-tmp' 2>/dev/null | while read -r dir; do
            rmdir "$dir" 2>/dev/null
        done
    done

    # 计算清理的总大小
    if [ -f "$temp_file" ]; then
        cleaned_size=$(awk '{sum += $1} END {print sum+0}' "$temp_file")
        rm -f "$temp_file"
    fi

    if [ $cleaned_size -gt 0 ]; then
        log_success "垃圾文件清理完成，释放空间: ${cleaned_size}MB"
    else
        log_success "垃圾文件清理完成"
    fi
}

# 检查文件是否存在
check_file_exists() {
    local file_path="$1"
    local file_name=$(basename "$file_path")

    if [ ! -f "$file_path" ]; then
        log_info "文件不存在: $file_name"
        return 1  # 文件不存在
    fi

    log_success "文件已存在: $file_name"
    return 0  # 文件存在
}

# 全局进度计数器
TOTAL_FILES=6
CURRENT_FILE=0

# 输出进度信息 - 直接显示格式化的进度
output_progress() {
    local file_index="$1"
    local status="$2"
    local file_name="$3"

    case "$status" in
        "checking")
            echo "   ${file_index}/6  检查中... ${file_name}"
            ;;
        "downloading")
            echo "   ${file_index}/6  下载中... ${file_name}"
            ;;
        "100%")
            echo "   ${file_index}/6  ✅ 100% ${file_name}"
            ;;
        "failed")
            echo "   ${file_index}/6  ❌ 失败 ${file_name}"
            ;;
    esac
}

# 标准化的下载和清理函数
download_and_cleanup() {
    local type_name="$1"
    local target_dir="$2"
    shift 2
    local files=("$@")
    local file_count=${#files[@]}
    local file_index=0
    local success_count=0

    mkdir -p "$target_dir"

    for file_url in "${files[@]}"; do
        file_index=$((file_index + 1))
        CURRENT_FILE=$((CURRENT_FILE + 1))
        local file_name=$(basename "$file_url")
        local file_path="$target_dir/$file_name"

        # 简化文件名显示
        local display_name="$file_name"
        case "$file_name" in
            "diffusion_pytorch_model-00001-of-00003.safetensors") display_name="transformer-1" ;;
            "diffusion_pytorch_model-00002-of-00003.safetensors") display_name="transformer-2" ;;
            "diffusion_pytorch_model-00003-of-00003.safetensors") display_name="transformer-3" ;;
            "model-00001-of-00002.safetensors") display_name="text_encoder-1" ;;
            "model-00002-of-00002.safetensors") display_name="text_encoder-2" ;;
            "flux1-kontext-dev.safetensors") display_name="flux1-kontext-dev" ;;
        esac

        output_progress "$CURRENT_FILE" "checking" "$display_name"
        log_info "处理 ${type_name} (${file_index}/${file_count}): ${file_name}"

        # 检查文件是否已存在
        if check_file_exists "$file_path"; then
            output_progress "$CURRENT_FILE" "100%" "$display_name"
            log_success "${type_name} (${file_index}/${file_count}) 文件已存在: ${file_name}"
            success_count=$((success_count + 1))
            continue
        fi

        # 下载文件
        output_progress "$CURRENT_FILE" "downloading" "$display_name"
        log_info "开始下载 ${type_name} (${file_index}/${file_count}): ${file_url}"
        cd "$target_dir"

        if cg down "$file_url"; then
            # 验证下载的文件
            if check_file_integrity "$file_path" $min_size_mb; then
                output_progress "$CURRENT_FILE" "100%" "$display_name"
                log_success "${type_name} (${file_index}/${file_count}) 下载完成: ${file_name}"
                success_count=$((success_count + 1))
            else
                output_progress "$CURRENT_FILE" "failed" "$display_name"
                log_error "${type_name} (${file_index}/${file_count}) 下载失败或文件不完整: ${file_name}"
            fi
        else
            output_progress "$CURRENT_FILE" "failed" "$display_name"
            log_error "${type_name} (${file_index}/${file_count}) 下载失败: ${file_name}"
        fi
    done

    # 移动所有文件到目标目录根目录并删除空的子目录
    find "$target_dir" -mindepth 2 -type f -exec mv -n -t "$target_dir" {} + 2>/dev/null
    find "$target_dir" -type d -empty -delete 2>/dev/null

    log_info "${type_name} 处理完成: ${success_count}/${file_count} 文件成功"
    return $((file_count - success_count))  # 返回失败的文件数量
}

# 处理每个模型类型的函数
download_transformer() {
    local transformer_files=(
        zealman/FLUX.1-Kontext-dev-train/diffusion_pytorch_model-00001-of-00003.safetensors
        zealman/FLUX.1-Kontext-dev-train/diffusion_pytorch_model-00002-of-00003.safetensors
        zealman/FLUX.1-Kontext-dev-train/diffusion_pytorch_model-00003-of-00003.safetensors
    )
    download_and_cleanup "transformer模型" /root/autodl-tmp/black-forest-labs/FLUX.1-Kontext-dev/transformer "${transformer_files[@]}"
}

# 处理 text_encoder_2 模型
download_text_encoder_2() {
    local text_encoder_2_files=(
        zealman/FLUX.1-Kontext-dev-train/model-00001-of-00002.safetensors
        zealman/FLUX.1-Kontext-dev-train/model-00002-of-00002.safetensors
    )
    download_and_cleanup "text_encoder_2模型" /root/autodl-tmp/black-forest-labs/FLUX.1-Kontext-dev/text_encoder_2 "${text_encoder_2_files[@]}"
}

# 处理 flux1-kontext-dev 模型
download_flux1_kontext_dev() {
    local flux1_kontext_dev_files=(
        zealman/FLUX.1-Kontext-dev-train/flux1-kontext-dev.safetensors
    )

    local target_dir="/root/autodl-tmp/black-forest-labs/FLUX.1-Kontext-dev"
    mkdir -p "$target_dir"

    local file_url="${flux1_kontext_dev_files[0]}"
    local file_name=$(basename "$file_url")
    local file_path="$target_dir/$file_name"
    local display_name="flux1-kontext-dev"

    CURRENT_FILE=$((CURRENT_FILE + 1))
    output_progress "$CURRENT_FILE" "checking" "$display_name"
    log_info "处理 flux1-kontext-dev模型: ${file_name}"

    # 检查文件是否已存在且完整
    if check_file_integrity "$file_path" 20000; then
        output_progress "$CURRENT_FILE" "100%" "$display_name"
        log_success "flux1-kontext-dev模型已存在且完整: $file_name"
        return 0
    fi

    output_progress "$CURRENT_FILE" "downloading" "$display_name"
    log_info "开始下载 flux1-kontext-dev模型: $file_url"
    cd "$target_dir"

    if cg down "$file_url"; then
        # 下载完成后，将深层目录中的文件移动到根目录
        find "$target_dir" -type f -name "flux1-kontext-dev.safetensors" -mindepth 2 -maxdepth 4 -exec mv -f {} "$target_dir/" \; 2>/dev/null
        find "$target_dir" -type d -empty -delete 2>/dev/null

        # 验证最终文件
        if check_file_integrity "$file_path" 20000; then
            output_progress "$CURRENT_FILE" "100%" "$display_name"
            log_success "flux1-kontext-dev模型下载完成: $file_name"
            return 0
        else
            output_progress "$CURRENT_FILE" "failed" "$display_name"
            log_error "flux1-kontext-dev模型下载失败或文件不完整: $file_name"
            return 1
        fi
    else
        output_progress "$CURRENT_FILE" "failed" "$display_name"
        log_error "flux1-kontext-dev模型下载失败: $file_name"
        return 1
    fi
}

# 检查所有模型是否存在
check_all_models() {
    log_info "正在检查所有模型文件是否存在..."

    local target_path="/root/autodl-tmp/black-forest-labs/FLUX.1-Kontext-dev"
    local missing_files=()

    # 检查transformer模型文件
    local transformer_files=(
        "transformer/diffusion_pytorch_model-00001-of-00003.safetensors"
        "transformer/diffusion_pytorch_model-00002-of-00003.safetensors"
        "transformer/diffusion_pytorch_model-00003-of-00003.safetensors"
    )

    for file_path in "${transformer_files[@]}"; do
        if ! check_file_exists "$target_path/$file_path" >/dev/null 2>&1; then
            missing_files+=("$file_path")
        fi
    done

    # 检查text_encoder_2模型文件
    local text_encoder_files=(
        "text_encoder_2/model-00001-of-00002.safetensors"
        "text_encoder_2/model-00002-of-00002.safetensors"
    )

    for file_path in "${text_encoder_files[@]}"; do
        if ! check_file_exists "$target_path/$file_path" >/dev/null 2>&1; then
            missing_files+=("$file_path")
        fi
    done

    # 检查主模型文件
    if ! check_file_exists "$target_path/flux1-kontext-dev.safetensors" >/dev/null 2>&1; then
        missing_files+=("flux1-kontext-dev.safetensors")
    fi

    if [ ${#missing_files[@]} -eq 0 ]; then
        log_success "所有模型文件已存在，无需下载"
        return 0
    else
        log_info "发现 ${#missing_files[@]} 个文件需要下载"
        return 1
    fi
}

# 主函数
main() {
    log_info "=== FLUX.1-Kontext-dev 模型管理脚本 ==="

    # 步骤1: 检查磁盘空间
    check_disk_space

    # 步骤2: 清理垃圾文件
    clean_cache

    # 步骤3: 检查模型文件是否存在
    echo ""
    echo "📊 模型下载进度:"

    if check_all_models; then
        # 输出所有文件的100%进度
        output_progress "1" "100%" "transformer-1"
        output_progress "2" "100%" "transformer-2"
        output_progress "3" "100%" "transformer-3"
        output_progress "4" "100%" "text_encoder-1"
        output_progress "5" "100%" "text_encoder-2"
        output_progress "6" "100%" "flux1-kontext-dev"
        echo ""
        log_success "所有模型已准备就绪！"
        exit 0
    fi

    # 步骤4: 根据参数下载模型
    local download_type="$1"
    local failed_downloads=0

    case "$download_type" in
        "transformer")
            log_info "开始下载 transformer 模型..."
            download_transformer
            failed_downloads=$?
            ;;
        "text_encoder_2")
            log_info "开始下载 text_encoder_2 模型..."
            download_text_encoder_2
            failed_downloads=$?
            ;;
        "flux1-kontext-dev")
            log_info "开始下载 flux1-kontext-dev 模型..."
            download_flux1_kontext_dev
            failed_downloads=$?
            ;;
        ""|"all")
            log_info "开始下载所有模型..."

            log_info "=== 第1步: 复制基础模型文件 ==="
            if [ -f "/root/scripts-aitoolkit/copy_fluxkontext_model.sh" ]; then
                bash /root/scripts-aitoolkit/copy_fluxkontext_model.sh
            fi

            echo ""
            echo "📊 模型下载进度:"

            log_info "=== 第2步: 下载 transformer 模型 ==="
            download_transformer
            failed_downloads=$((failed_downloads + $?))

            log_info "=== 第3步: 下载 text_encoder_2 模型 ==="
            download_text_encoder_2
            failed_downloads=$((failed_downloads + $?))

            log_info "=== 第4步: 下载 flux1-kontext-dev 模型 ==="
            download_flux1_kontext_dev
            failed_downloads=$((failed_downloads + $?))
            ;;
        *)
            log_error "未知的下载类型: $download_type"
            log_info "用法: $0 [transformer|text_encoder_2|flux1-kontext-dev|all]"
            exit 1
            ;;
    esac

    # 步骤5: 最终验证
    log_info "=== 最终验证 ==="
    if check_all_models; then
        log_success "✅ 所有模型文件下载完成且验证通过！"
        log_success "模型已准备齐全，可以开始训练了！"
        exit 0
    else
        log_error "❌ 部分模型文件下载失败或不完整"
        log_error "请检查网络连接后重试"
        exit 1
    fi
}

# 执行主函数
main "$@"