#!/bin/bash

# 标准化的下载和清理函数
download_and_cleanup() {
  local type_name=$1      # 模型类型名称，例如 "VAE模型"
  local target_dir=$2     # 目标目录
  shift 2                 # 移除前两个参数，剩下的就是文件列表

  local files=("$@")      # 文件数组
  local file_count=${#files[@]}
  local file_index=0      # 当前类型的文件计数器

  mkdir -p "$target_dir" && cd "$target_dir"

  for file_url in "${files[@]}"; do
    file_index=$((file_index + 1))
    local file_name=$(basename "$file_url")
    local file_path="$target_dir/$file_name"

    # 检查文件是否已存在
    if [ -f "$file_path" ]; then
      echo "${type_name} (${file_index}/${file_count}) 文件已存在，跳过: ${file_name}"
      continue
    fi

    # 下载文件
    echo "开始下载 ${type_name} (${file_index}/${file_count}): ${file_url}"
    echo ">>> 下载到 ${file_path}"
    cg down "$file_url"   # 请确保 'cg down' 是有效的命令，或替换为 wget/curl
    echo "${type_name} (${file_index}/${file_count}) 已下载完成"
  done

  # 移动所有文件到目标目录根目录并删除空的子目录
  find "$target_dir" -mindepth 2 -type f -exec mv -n -t "$target_dir" {} +
  # 删除移动后产生的空目录
  find "$target_dir" -type d -empty -delete
}

# 处理每个模型类型的函数
download_transformer() {
  local transformer_files=(
    zealman/FLUX.1-Kontext-dev-train/diffusion_pytorch_model-00001-of-00003.safetensors
    zealman/FLUX.1-Kontext-dev-train/diffusion_pytorch_model-00002-of-00003.safetensors 
    zealman/FLUX.1-Kontext-dev-train/diffusion_pytorch_model-00003-of-00003.safetensors
  )
  download_and_cleanup "transformer模型" /root/autodl-tmp/black-forest-labs/FLUX.1-Kontext-dev/transformer "${transformer_files[@]}"
}


# 处理 text_encoder_2 模型
download_text_encoder_2() {
  local text_encoder_2_files=(
    zealman/FLUX.1-Kontext-dev-train/model-00001-of-00002.safetensors
    zealman/FLUX.1-Kontext-dev-train/model-00002-of-00002.safetensors
  )
  download_and_cleanup "text_encoder_2模型" /root/autodl-tmp/black-forest-labs/FLUX.1-Kontext-dev/text_encoder_2 "${text_encoder_2_files[@]}"
}

# 处理 flux1-kontext-dev 模型
download_flux1_kontext_dev() {
  local flux1_kontext_dev_files=(
    zealman/FLUX.1-Kontext-dev-train/flux1-kontext-dev.safetensors
  )

  local target_dir="/root/autodl-tmp/black-forest-labs/FLUX.1-Kontext-dev"
  mkdir -p "$target_dir" && cd "$target_dir"

  for file_url in "${flux1_kontext_dev_files[@]}"; do
    local file_name=$(basename "$file_url")

    # 如果文件已存在于根目录则跳过
    if [ -f "$target_dir/$file_name" ]; then
      echo "flux1-kontext-dev模型已存在，跳过: $file_name"
      continue
    fi

    echo "开始下载 flux1-kontext-dev模型: $file_url"
    cg down "$file_url"
  done

  # 下载完成后，将深层目录中的文件移动到根目录
  find "$target_dir" -type f -name "flux1-kontext-dev.safetensors" -mindepth 2 -maxdepth 4 -exec mv -f {} "$target_dir/" \;
  # 删除移动后产生的空目录
  find "$target_dir" -type d -empty -delete

  echo "flux1-kontext-dev模型已放置在根目录，并清理完毕"
}

# 检查传递的参数
download_type=$1

# 根据传递的参数选择要下载的内容
case "$download_type" in
  "transformer")
    download_transformer
    ;;
  "text_encoder_2")
    download_text_encoder_2
    ;;
  "flux1-kontext-dev")
    download_flux1_kontext_dev
    ;;
  "")
    # 默认下载所有模型
    download_transformer
    download_text_encoder_2
    download_flux1_kontext_dev
    ;;
  *)
    echo "未知的下载类型: $download_type"
    exit 1
    ;;
esac

# 完成提示
echo "模型已准备齐全，开始运行吧！"