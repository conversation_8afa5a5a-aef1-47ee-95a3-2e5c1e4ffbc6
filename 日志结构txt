📥 模型管理脚本运行中...
⏳ 请耐心等待，这一般来说1-2分钟内就OK
📋 正在执行：磁盘空间检查、垃圾文件清理、模型下载和验证

[INFO] === FLUX.1-Kontext-dev 模型管理脚本 ===
[INFO] 正在检查磁盘总空间...
[INFO] 当前磁盘总空间: 58GB
[SUCCESS] 磁盘空间充足 (58GB >= 58GB)
[INFO] 正在清理数据盘垃圾文件...
[SUCCESS] 垃圾文件清理完成，释放空间: 22701MB

📊 模型下载进度:
[INFO] 快速检查是否需要下载...
[INFO] 发现 6 个文件需要下载
[INFO] 开始下载所有模型...

[INFO] === 第1步: 复制基础模型文件 ===
开始拷贝FLUX.1-Kontext-dev模型文件...

✅ FLUX.1-Kontext-dev模型文件拷贝完成！
目标目录: /root/autodl-tmp/black-forest-labs/FLUX.1-Kontext-dev
已拷贝的文件和目录：
total 23578584
drwxr-xr-x 9 <USER> <GROUP>        4096 Jul  8 20:30 .
drwxr-xr-x 3 <USER> <GROUP>          40 Jul  8 18:52 ..
-rw-r--r-- 1 <USER> <GROUP>        1234 Jul  8 20:30 .gitattributes
-rw-r--r-- 1 <USER> <GROUP>       18621 Jul  8 20:30 LICENSE.md
-rw-r--r-- 1 <USER> <GROUP>        9640 Jul  8 20:30 README.md
-rw-r--r-- 1 <USER> <GROUP>   335304388 Jul  8 20:30 ae.safetensors
-rw-r--r-- 1 <USER> <GROUP> 23802947360 Jul  8 20:30 flux1-kontext-dev.safetensors
-rw-r--r-- 1 <USER> <GROUP>         688 Jul  8 20:30 model_index.json
-rw-r--r-- 1 <USER> <GROUP>     6171062 Jul  8 20:30 teaser.png
drwxr-xr-x 2 <USER> <GROUP>          43 Jul  8 20:30 scheduler
drwxr-xr-x 2 <USER> <GROUP>          62 Jul  8 20:30 text_encoder
drwxr-xr-x 2 <USER> <GROUP>         161 Jul  8 20:30 text_encoder_2
drwxr-xr-x 2 <USER> <GROUP>         122 Jul  8 20:30 tokenizer
drwxr-xr-x 2 <USER> <GROUP>         128 Jul  8 20:30 tokenizer_2
drwxr-xr-x 2 <USER> <GROUP>          91 Jul  8 20:30 transformer
drwxr-xr-x 2 <USER> <GROUP>          80 Jul  8 20:30 vae
模型文件准备完成，可以开始使用了！

📊 模型下载进度:
[INFO] === 第2步: 下载 transformer 模型 ===
   1/6  检查中... transformer-1
[INFO] 处理 transformer模型 (1/3): diffusion_pytorch_model-00001-of-00003.safetensors
[SUCCESS] transformer (1/6) 文件已存在于最终目录: diffusion_pytorch_model-00001-of-00003.safetensors
   1/6  100% transformer-1

   2/6  检查中... transformer-2
[INFO] 处理 transformer模型 (2/3): diffusion_pytorch_model-00002-of-00003.safetensors
   2/6  下载中... transformer-2
[INFO] 开始下载 transformer模型 (2/3): zealman/FLUX.1-Kontext-dev-train/diffusion_pytorch_model-00002-of-00003.safetensors
downloading file [FLUX.1-Kontext-dev-train/diffusion_pytorch_model-00002-of-00003.safetensors]

downloading... 8.62% 835 MiB/9.3 GiB
downloading... 17.24% 1.6 GiB/9.3 GiB
downloading... 25.86% 2.4 GiB/9.3 GiB
downloading... 34.48% 3.2 GiB/9.3 GiB
downloading... 43.10% 4.0 GiB/9.3 GiB
downloading... 51.72% 4.8 GiB/9.3 GiB
downloading... 60.34% 5.6 GiB/9.3 GiB
downloading... 68.96% 6.4 GiB/9.3 GiB
downloading... 77.58% 7.2 GiB/9.3 GiB
downloading... 86.20% 8.0 GiB/9.3 GiB
downloading... 94.82% 8.8 GiB/9.3 GiB
downloading... 100% 9.3 GiB/9.3 GiB
>>> download to  /root/autodl-tmp/black-forest-labs/FLUX.1-Kontext-dev/transformer/zealman/FLUX.1-Kontext-dev-train/diffusion_pytorch_model-00002-of-00003.safetensors
[SUCCESS] transformer模型 (2/3) 下载完成: diffusion_pytorch_model-00002-of-00003.safetensors
   2/6  100% transformer-2

   3/6  检查中... transformer-3
[INFO] 处理 transformer模型 (3/3): diffusion_pytorch_model-00003-of-00003.safetensors
   3/6  下载中... transformer-3
[INFO] 开始下载 transformer模型 (3/3): zealman/FLUX.1-Kontext-dev-train/diffusion_pytorch_model-00003-of-00003.safetensors
downloading file [FLUX.1-Kontext-dev-train/diffusion_pytorch_model-00003-of-00003.safetensors]

downloading... 12.50% 450 MiB/3.6 GiB
downloading... 25.00% 900 MiB/3.6 GiB
downloading... 37.50% 1.3 GiB/3.6 GiB
downloading... 50.00% 1.8 GiB/3.6 GiB
downloading... 62.50% 2.3 GiB/3.6 GiB
downloading... 75.00% 2.7 GiB/3.6 GiB
downloading... 87.50% 3.2 GiB/3.6 GiB
downloading... 100% 3.6 GiB/3.6 GiB
>>> download to  /root/autodl-tmp/black-forest-labs/FLUX.1-Kontext-dev/transformer/zealman/FLUX.1-Kontext-dev-train/diffusion_pytorch_model-00003-of-00003.safetensors
[SUCCESS] transformer模型 (3/3) 下载完成: diffusion_pytorch_model-00003-of-00003.safetensors
   3/6  100% transformer-3

[INFO] === 第3步: 下载 text_encoder_2 模型 ===
   4/6  检查中... text_encoder_2-1
[INFO] 处理 text_encoder_2模型 (1/2): model-00001-of-00002.safetensors
   4/6  下载中... text_encoder_2-1
[INFO] 开始下载 text_encoder_2模型 (1/2): zealman/FLUX.1-Kontext-dev-train/model-00001-of-00002.safetensors
downloading file [FLUX.1-Kontext-dev-train/model-00001-of-00002.safetensors]

downloading... 10.75% 500 MiB/4.6 GiB
downloading... 21.50% 1.0 GiB/4.6 GiB
downloading... 32.25% 1.5 GiB/4.6 GiB
downloading... 43.00% 2.0 GiB/4.6 GiB
downloading... 53.75% 2.5 GiB/4.6 GiB
downloading... 64.50% 3.0 GiB/4.6 GiB
downloading... 75.25% 3.5 GiB/4.6 GiB
downloading... 86.00% 4.0 GiB/4.6 GiB
downloading... 96.75% 4.5 GiB/4.6 GiB
downloading... 100% 4.6 GiB/4.6 GiB
>>> download to  /root/autodl-tmp/black-forest-labs/FLUX.1-Kontext-dev/text_encoder_2/zealman/FLUX.1-Kontext-dev-train/model-00001-of-00002.safetensors
[SUCCESS] text_encoder_2模型 (1/2) 下载完成: model-00001-of-00002.safetensors
   4/6  100% text_encoder_2-1

   5/6  检查中... text_encoder_2-2
[INFO] 处理 text_encoder_2模型 (2/2): model-00002-of-00002.safetensors
   5/6  下载中... text_encoder_2-2
[INFO] 开始下载 text_encoder_2模型 (2/2): zealman/FLUX.1-Kontext-dev-train/model-00002-of-00002.safetensors
downloading file [FLUX.1-Kontext-dev-train/model-00002-of-00002.safetensors]

downloading... 11.90% 500 MiB/4.2 GiB
downloading... 23.80% 1.0 GiB/4.2 GiB
downloading... 35.70% 1.5 GiB/4.2 GiB
downloading... 47.60% 2.0 GiB/4.2 GiB
downloading... 59.50% 2.5 GiB/4.2 GiB
downloading... 71.40% 3.0 GiB/4.2 GiB
downloading... 83.30% 3.5 GiB/4.2 GiB
downloading... 95.20% 4.0 GiB/4.2 GiB
downloading... 100% 4.2 GiB/4.2 GiB
>>> download to  /root/autodl-tmp/black-forest-labs/FLUX.1-Kontext-dev/text_encoder_2/zealman/FLUX.1-Kontext-dev-train/model-00002-of-00002.safetensors
[SUCCESS] text_encoder_2模型 (2/2) 下载完成: model-00002-of-00002.safetensors
   5/6  100% text_encoder_2-2

[INFO] === 第4步: 下载 flux1-kontext-dev 模型 ===
   6/6  检查中... flux1-kontext-dev
[INFO] 处理 flux1-kontext-dev模型: flux1-kontext-dev.safetensors
[SUCCESS] flux1-kontext-dev模型已存在: flux1-kontext-dev.safetensors
   6/6  100% flux1-kontext-dev

[INFO] 正在整理文件结构...
[INFO] 移动下载的文件到最终目录...
[SUCCESS] 文件移动完成

[INFO] === 最终验证 ===
[INFO] 正在检查最终目录中的所有模型文件...
[SUCCESS] 文件已存在: diffusion_pytorch_model-00001-of-00003.safetensors
[SUCCESS] 文件已存在: diffusion_pytorch_model-00002-of-00003.safetensors
[SUCCESS] 文件已存在: diffusion_pytorch_model-00003-of-00003.safetensors
[SUCCESS] 文件已存在: model-00001-of-00002.safetensors
[SUCCESS] 文件已存在: model-00002-of-00002.safetensors
[SUCCESS] 文件已存在: flux1-kontext-dev.safetensors
[SUCCESS] ✅ 所有模型文件下载完成且验证通过！
[SUCCESS] 模型已准备齐全，可以开始训练了！

📊 最终模型结构:
/root/autodl-tmp/black-forest-labs/FLUX.1-Kontext-dev/
├── .gitattributes
├── LICENSE.md
├── README.md
├── ae.safetensors (335MB)
├── flux1-kontext-dev.safetensors (22.1GB)
├── model_index.json
├── teaser.png
├── scheduler/
│   └── scheduler_config.json
├── text_encoder/
│   ├── config.json
│   └── model.safetensors
├── text_encoder_2/
│   ├── config.json
│   ├── model-00001-of-00002.safetensors (4.65GB)
│   ├── model-00002-of-00002.safetensors (4.21GB)
│   └── model.safetensors.index.json
├── tokenizer/
│   ├── merges.txt
│   ├── special_tokens_map.json
│   ├── tokenizer_config.json
│   └── vocab.json
├── tokenizer_2/
│   ├── special_tokens_map.json
│   ├── spiece.model
│   ├── tokenizer.json
│   └── tokenizer_config.json
├── transformer/
│   ├── config.json
│   ├── diffusion_pytorch_model-00001-of-00003.safetensors (9.29GB)
│   ├── diffusion_pytorch_model-00002-of-00003.safetensors (9.26GB)
│   ├── diffusion_pytorch_model-00003-of-00003.safetensors (3.60GB)
│   └── diffusion_pytorch_model.safetensors.index.json
└── vae/
    ├── config.json
    └── diffusion_pytorch_model.safetensors

✅ 模型下载和验证完成！
📊 总下载大小: ~50GB
⏱️  总耗时: 约15-20分钟
🎯 状态: 所有文件已就绪，可以开始AI训练！
