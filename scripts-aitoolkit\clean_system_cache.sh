#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "系统盘垃圾文件清理脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -v, --verbose  详细模式，显示清理的文件"
    echo "  -d, --dry-run  预览模式，只显示将要清理的文件，不实际删除"
    echo "  -s, --silent   静默模式，不显示详细信息"
    echo "  -a, --all      清理所有内容（包括较大的缓存）"
    echo ""
    echo "清理内容:"
    echo "  • Python缓存 (pip cache, __pycache__, .pyc文件)"
    echo "  • Node.js缓存 (npm cache, node_modules/.cache)"
    echo "  • 系统临时文件 (/tmp, /var/tmp)"
    echo "  • 日志文件 (/var/log/*.log)"
    echo "  • APT缓存 (/var/cache/apt)"
    echo "  • 用户缓存 (~/.cache, ~/.local/share/Trash)"
    echo "  • Conda/Mamba缓存"
    echo "  • Docker缓存 (需要--all参数)"
    echo "  • 系统包管理器缓存"
    echo ""
    echo "示例:"
    echo "  $0              # 基本清理"
    echo "  $0 -v           # 详细模式清理"
    echo "  $0 -d           # 预览将要清理的文件"
    echo "  $0 -a           # 清理所有内容（包括Docker等）"
    echo "  $0 -s           # 静默清理（适合脚本调用）"
}

# 安全检查函数
safe_remove() {
    local path="$1"
    local verbose="$2"
    local dry_run="$3"
    
    if [ ! -e "$path" ]; then
        return 0
    fi
    
    if [ "$verbose" = "true" ]; then
        if [ -d "$path" ]; then
            local size=$(du -sh "$path" 2>/dev/null | cut -f1 || echo "0")
            echo "  清理目录: $path ($size)"
        else
            local size=$(du -sh "$path" 2>/dev/null | cut -f1 || echo "0")
            echo "  清理文件: $path ($size)"
        fi
    fi
    
    if [ "$dry_run" != "true" ]; then
        rm -rf "$path" 2>/dev/null
    fi
}

# 清理Python相关缓存
clean_python_cache() {
    local verbose=$1
    local dry_run=$2
    local silent=$3
    
    if [ "$silent" != "true" ]; then
        log_info "清理Python缓存..."
    fi
    
    # 清理pip缓存
    if command -v pip >/dev/null 2>&1; then
        if [ "$dry_run" != "true" ]; then
            pip cache purge >/dev/null 2>&1
        fi
        if [ "$verbose" = "true" ]; then
            echo "  清理pip缓存"
        fi
    fi
    
    if command -v pip3 >/dev/null 2>&1; then
        if [ "$dry_run" != "true" ]; then
            pip3 cache purge >/dev/null 2>&1
        fi
        if [ "$verbose" = "true" ]; then
            echo "  清理pip3缓存"
        fi
    fi
    
    # 清理__pycache__目录
    find /root /home 2>/dev/null -type d -name "__pycache__" | while read -r dir; do
        safe_remove "$dir" "$verbose" "$dry_run"
    done
    
    # 清理.pyc文件
    find /root /home 2>/dev/null -type f -name "*.pyc" | while read -r file; do
        safe_remove "$file" "$verbose" "$dry_run"
    done
    
    # 清理用户pip缓存目录
    for user_home in /root /home/<USER>
        if [ -d "$user_home" ]; then
            safe_remove "$user_home/.cache/pip" "$verbose" "$dry_run"
            safe_remove "$user_home/.local/share/pip" "$verbose" "$dry_run"
        fi
    done
}

# 清理Node.js相关缓存
clean_nodejs_cache() {
    local verbose=$1
    local dry_run=$2
    local silent=$3
    
    if [ "$silent" != "true" ]; then
        log_info "清理Node.js缓存..."
    fi
    
    # 清理npm缓存
    if command -v npm >/dev/null 2>&1; then
        if [ "$dry_run" != "true" ]; then
            npm cache clean --force >/dev/null 2>&1
        fi
        if [ "$verbose" = "true" ]; then
            echo "  清理npm缓存"
        fi
    fi
    
    # 清理yarn缓存
    if command -v yarn >/dev/null 2>&1; then
        if [ "$dry_run" != "true" ]; then
            yarn cache clean >/dev/null 2>&1
        fi
        if [ "$verbose" = "true" ]; then
            echo "  清理yarn缓存"
        fi
    fi
    
    # 清理用户npm缓存
    for user_home in /root /home/<USER>
        if [ -d "$user_home" ]; then
            safe_remove "$user_home/.npm" "$verbose" "$dry_run"
            safe_remove "$user_home/.yarn/cache" "$verbose" "$dry_run"
        fi
    done
}

# 清理Conda/Mamba缓存
clean_conda_cache() {
    local verbose=$1
    local dry_run=$2
    local silent=$3
    
    if [ "$silent" != "true" ]; then
        log_info "清理Conda/Mamba缓存..."
    fi
    
    # 清理conda缓存
    if command -v conda >/dev/null 2>&1; then
        if [ "$dry_run" != "true" ]; then
            conda clean -a -y >/dev/null 2>&1
        fi
        if [ "$verbose" = "true" ]; then
            echo "  清理conda缓存"
        fi
    fi
    
    # 清理mamba缓存
    if command -v mamba >/dev/null 2>&1; then
        if [ "$dry_run" != "true" ]; then
            mamba clean -a -y >/dev/null 2>&1
        fi
        if [ "$verbose" = "true" ]; then
            echo "  清理mamba缓存"
        fi
    fi
    
    # 清理用户conda缓存
    for user_home in /root /home/<USER>
        if [ -d "$user_home" ]; then
            safe_remove "$user_home/.conda/pkgs" "$verbose" "$dry_run"
            safe_remove "$user_home/.conda/envs/.conda_envs_dir_test" "$verbose" "$dry_run"
        fi
    done
}

# 清理系统临时文件
clean_system_temp() {
    local verbose=$1
    local dry_run=$2
    local silent=$3
    
    if [ "$silent" != "true" ]; then
        log_info "清理系统临时文件..."
    fi
    
    # 清理/tmp目录（保留目录本身）
    find /tmp -mindepth 1 -maxdepth 1 2>/dev/null | while read -r item; do
        safe_remove "$item" "$verbose" "$dry_run"
    done
    
    # 清理/var/tmp目录
    find /var/tmp -mindepth 1 -maxdepth 1 2>/dev/null | while read -r item; do
        safe_remove "$item" "$verbose" "$dry_run"
    done
    
    # 清理系统日志文件
    find /var/log -name "*.log" -type f -size +10M 2>/dev/null | while read -r log_file; do
        if [ "$verbose" = "true" ]; then
            echo "  清理大日志文件: $log_file"
        fi
        if [ "$dry_run" != "true" ]; then
            > "$log_file"  # 清空而不是删除
        fi
    done
}

# 清理APT缓存
clean_apt_cache() {
    local verbose=$1
    local dry_run=$2
    local silent=$3
    
    if [ "$silent" != "true" ]; then
        log_info "清理APT缓存..."
    fi
    
    if command -v apt-get >/dev/null 2>&1; then
        if [ "$dry_run" != "true" ]; then
            apt-get clean >/dev/null 2>&1
            apt-get autoclean >/dev/null 2>&1
            apt-get autoremove -y >/dev/null 2>&1
        fi
        if [ "$verbose" = "true" ]; then
            echo "  清理APT缓存和无用包"
        fi
    fi
    
    safe_remove "/var/cache/apt/archives/*.deb" "$verbose" "$dry_run"
    safe_remove "/var/cache/apt/archives/partial/*" "$verbose" "$dry_run"
}

# 清理用户缓存
clean_user_cache() {
    local verbose=$1
    local dry_run=$2
    local silent=$3
    
    if [ "$silent" != "true" ]; then
        log_info "清理用户缓存..."
    fi
    
    for user_home in /root /home/<USER>
        if [ -d "$user_home" ]; then
            # 清理用户缓存目录
            safe_remove "$user_home/.cache/thumbnails" "$verbose" "$dry_run"
            safe_remove "$user_home/.cache/fontconfig" "$verbose" "$dry_run"
            safe_remove "$user_home/.cache/mesa_shader_cache" "$verbose" "$dry_run"
            
            # 清理垃圾箱
            safe_remove "$user_home/.local/share/Trash" "$verbose" "$dry_run"
            safe_remove "$user_home/.Trash" "$verbose" "$dry_run"
            
            # 清理浏览器缓存
            safe_remove "$user_home/.cache/google-chrome" "$verbose" "$dry_run"
            safe_remove "$user_home/.cache/chromium" "$verbose" "$dry_run"
            safe_remove "$user_home/.cache/mozilla" "$verbose" "$dry_run"
        fi
    done
}

# 清理Docker缓存（需要--all参数）
clean_docker_cache() {
    local verbose=$1
    local dry_run=$2
    local silent=$3

    if [ "$silent" != "true" ]; then
        log_info "清理Docker缓存..."
    fi

    if command -v docker >/dev/null 2>&1; then
        if [ "$dry_run" != "true" ]; then
            docker system prune -f >/dev/null 2>&1
            docker volume prune -f >/dev/null 2>&1
        fi
        if [ "$verbose" = "true" ]; then
            echo "  清理Docker系统缓存"
        fi
    fi
}

# 获取磁盘使用情况
get_disk_usage() {
    local path="$1"
    df -h "$path" 2>/dev/null | awk 'NR==2 {print $3, $4, $5}' || echo "0 0 0%"
}

# 主清理函数
clean_system_cache() {
    local verbose=$1
    local dry_run=$2
    local silent=$3
    local clean_all=$4

    if [ "$silent" != "true" ]; then
        if [ "$dry_run" = "true" ]; then
            log_info "=== 系统盘垃圾清理预览 ==="
        else
            log_info "=== 开始清理系统盘垃圾文件 ==="
        fi

        # 显示清理前的磁盘使用情况
        local disk_info_before=$(get_disk_usage "/")
        local used_before=$(echo $disk_info_before | cut -d' ' -f1)
        local avail_before=$(echo $disk_info_before | cut -d' ' -f2)
        local percent_before=$(echo $disk_info_before | cut -d' ' -f3)

        log_info "清理前磁盘使用: ${used_before} 已用, ${avail_before} 可用 (${percent_before})"
    fi

    # 执行各种清理
    clean_python_cache "$verbose" "$dry_run" "$silent"
    clean_nodejs_cache "$verbose" "$dry_run" "$silent"
    clean_conda_cache "$verbose" "$dry_run" "$silent"
    clean_system_temp "$verbose" "$dry_run" "$silent"
    clean_apt_cache "$verbose" "$dry_run" "$silent"
    clean_user_cache "$verbose" "$dry_run" "$silent"

    # 如果指定了--all参数，清理更多内容
    if [ "$clean_all" = "true" ]; then
        clean_docker_cache "$verbose" "$dry_run" "$silent"
    fi

    if [ "$silent" != "true" ]; then
        # 显示清理后的磁盘使用情况
        if [ "$dry_run" != "true" ]; then
            sleep 1  # 等待文件系统更新
            local disk_info_after=$(get_disk_usage "/")
            local used_after=$(echo $disk_info_after | cut -d' ' -f1)
            local avail_after=$(echo $disk_info_after | cut -d' ' -f2)
            local percent_after=$(echo $disk_info_after | cut -d' ' -f3)

            log_success "清理后磁盘使用: ${used_after} 已用, ${avail_after} 可用 (${percent_after})"
            log_success "系统盘垃圾清理完成！"
        else
            log_info "=== 预览完成 ==="
            log_warning "这是预览模式，没有实际删除任何文件"
        fi
    fi
}

# 主函数
main() {
    local verbose=false
    local dry_run=false
    local silent=false
    local clean_all=false

    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--verbose)
                verbose=true
                shift
                ;;
            -d|--dry-run)
                dry_run=true
                shift
                ;;
            -s|--silent)
                silent=true
                shift
                ;;
            -a|--all)
                clean_all=true
                shift
                ;;
            *)
                log_error "未知参数: $1"
                log_info "使用 -h 或 --help 查看帮助信息"
                exit 1
                ;;
        esac
    done

    # 检查是否有root权限
    if [ "$EUID" -ne 0 ] && [ "$dry_run" != "true" ]; then
        log_warning "建议以root权限运行以获得最佳清理效果"
        log_info "当前将跳过需要root权限的清理项目"
    fi

    # 执行清理
    clean_system_cache "$verbose" "$dry_run" "$silent" "$clean_all"
}

# 执行主函数
main "$@"
