#!/bin/bash

# 测试清理脚本集成
echo "=== 测试清理脚本集成 ==="

# 测试独立清理脚本
echo ""
echo "1. 测试独立清理脚本..."
if [ -f "/root/scripts-aitoolkit/clean_cache.sh" ]; then
    echo "✅ 清理脚本存在"
    echo "预览清理内容："
    bash /root/scripts-aitoolkit/clean_cache.sh -d
else
    echo "❌ 清理脚本不存在"
fi

# 测试下载脚本中的清理函数
echo ""
echo "2. 测试下载脚本中的清理函数..."
if [ -f "/root/scripts-aitoolkit/down_blackfluxkontextmodel.sh" ]; then
    echo "✅ 下载脚本存在"
    
    # 提取并测试clean_cache函数
    source /root/scripts-aitoolkit/down_blackfluxkontextmodel.sh
    
    echo "测试clean_cache函数调用..."
    clean_cache
else
    echo "❌ 下载脚本不存在"
fi

echo ""
echo "=== 测试完成 ==="
