{"cells": [{"cell_type": "markdown", "metadata": {"vscode": {"languageId": "plaintext"}}, "source": ["介绍\n", "- AI-toolkit 是一款专为  模型训练设计的工具\n", "- 已更新到 **2025年7月8日版本** 对应官方 0.3.4\n", "- 本镜像只保留了**FLUX Kontext Lora**的训练，其他选项已隐藏\n", "\n", "使用说明\n", "1. 点击下方 **「1.下载模型」** 按钮，等待下载完成\n", "2. 点击 **「2.启动训练UI」** 按钮启动服务\n", "3. 访问 [http://localhost:6006](http://localhost:6006) 打开训练界面\n", "4. 如使用 Autodl-ssh 工具连接，端口填写 **6006**\n", "\n", "注意事项\n", "- 🔴 **重要**: 开机前需要将数据盘扩充 **10G** （训练底模需要较大空间）\n", "- 💬 有问题可以加 Q群：**1046279436**\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["from IPython.display import HTML, Javascript, display, clear_output\n", "import ipywidgets as widgets\n", "import subprocess\n", "import threading\n", "import time\n", "import os\n", "\n", "# 清理之前的输出\n", "clear_output()\n", "\n", "# 注入苹果风格 CSS\n", "display(HTML(\"\"\"\n", "<style>\n", "/* 苹果风格按钮 */\n", ".apple-btn {\n", "    background: linear-gradient(180deg, #0A84FF 0%, #0060DF 100%);\n", "    color: #FFFFFF !important;\n", "    border: none;\n", "    border-radius: 10px;\n", "    padding: 12px 24px;\n", "    font-size: 15px;\n", "    font-weight: 500;\n", "    box-shadow: 0 3px 8px rgba(0,0,0,0.15);\n", "    transition: all 0.2s ease;\n", "    min-width: 160px;\n", "    display: inline-block !important;\n", "    height: auto !important;\n", "    line-height: normal !important;\n", "}\n", ".apple-btn:hover {\n", "    background: linear-gradient(180deg, #379AFF 0%, #007AFF 100%);\n", "    box-shadow: 0 4px 12px rgba(0,0,0,0.2);\n", "    transform: translateY(-1px);\n", "}\n", ".apple-btn:active {\n", "    transform: scale(0.98);\n", "    box-shadow: 0 2px 4px rgba(0,0,0,0.25);\n", "}\n", ".apple-btn:disabled {\n", "    background: #E5E5EA !important;\n", "    color: #8E8E93 !important;\n", "    box-shadow: none !important;\n", "    cursor: not-allowed !important;\n", "    opacity: 0.6 !important;\n", "}\n", "\n", "/* JupyterLab 特定的禁用状态样式 */\n", ".widget-button[disabled] {\n", "    background: #E5E5EA !important;\n", "    color: #8E8E93 !important;\n", "    opacity: 0.6 !important;\n", "    pointer-events: none !important;\n", "}\n", "\n", "/* 修复 Jupyter 按钮样式 */\n", ".widget-button {\n", "    overflow: visible !important;\n", "}\n", ".jupyter-widgets.widget-button .widget-button {\n", "    height: auto !important;\n", "}\n", "\n", "/* 输出区域样式 */\n", ".widget-output {\n", "    background: #F2F2F7;\n", "    border-radius: 8px;\n", "    padding: 12px 16px;\n", "    margin-top: 12px;\n", "}\n", ".widget-output .output_area {\n", "    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;\n", "    font-size: 13px;\n", "    color: #1C1C1E;\n", "}\n", "\n", "/* 状态指示器 */\n", ".status-indicator {\n", "    display: inline-block;\n", "    width: 8px;\n", "    height: 8px;\n", "    border-radius: 50%;\n", "    margin-right: 8px;\n", "    animation: pulse 2s infinite;\n", "}\n", ".status-success { background: #34C759; }\n", ".status-running { background: #FF9500; }\n", ".status-error { background: #FF3B30; }\n", "\n", "@keyframes pulse {\n", "    0% { opacity: 1; }\n", "    50% { opacity: 0.5; }\n", "    100% { opacity: 1; }\n", "}\n", "\n", "/* 修复整体布局 */\n", ".widget-vbox {\n", "    gap: 0 !important;\n", "}\n", ".jp-OutputArea-output {\n", "    overflow: visible !important;\n", "}\n", "</style>\n", "\"\"\"))\n", "\n", "# 工具函数：检查磁盘空间\n", "def check_disk_space():\n", "    \"\"\"检查 /root/autodl-tmp/ 目录可用空间（GB）\"\"\"\n", "    try:\n", "        result = subprocess.run(['df', '/root/autodl-tmp/', '--output=avail'], \n", "                              stdout=subprocess.PIPE, text=True)\n", "        if result.returncode == 0:\n", "            lines = result.stdout.strip().split('\\n')\n", "            if len(lines) >= 2:\n", "                avail_kb = int(lines[1])\n", "                avail_gb = avail_kb / (1024 * 1024)\n", "                return avail_gb\n", "    except:\n", "        pass\n", "    return 0\n", "\n", "# 工具函数：检查模型是否已存在\n", "def check_models_exist():\n", "    \"\"\"检查模型文件是否已经存在\"\"\"\n", "    # 根据实际脚本中的路径检查\n", "    target_path = '/root/autodl-tmp/black-forest-labs/FLUX.1-Kontext-dev'\n", "    \n", "    if not os.path.exists(target_path):\n", "        return False, None\n", "    \n", "    # 检查必要的模型文件（基于实际脚本内容）\n", "    required_files = {\n", "        'transformer': [\n", "            'diffusion_pytorch_model-00001-of-00003.safetensors',\n", "            'diffusion_pytorch_model-00002-of-00003.safetensors', \n", "            'diffusion_pytorch_model-00003-of-00003.safetensors'\n", "        ],\n", "        'text_encoder_2': [\n", "            'model-00001-of-00002.safetensors',\n", "            'model-00002-of-00002.safetensors'\n", "        ],\n", "        'main_model': ['flux1-kontext-dev.safetensors']\n", "    }\n", "    \n", "    missing_files = []\n", "    \n", "    # 检查transformer模型文件\n", "    transformer_path = os.path.join(target_path, 'transformer')\n", "    if os.path.exists(transformer_path):\n", "        for file in required_files['transformer']:\n", "            if not os.path.exists(os.path.join(transformer_path, file)):\n", "                missing_files.append(f'transformer/{file}')\n", "    else:\n", "        missing_files.extend([f'transformer/{file}' for file in required_files['transformer']])\n", "    \n", "    # 检查text_encoder_2模型文件\n", "    text_encoder_path = os.path.join(target_path, 'text_encoder_2')\n", "    if os.path.exists(text_encoder_path):\n", "        for file in required_files['text_encoder_2']:\n", "            if not os.path.exists(os.path.join(text_encoder_path, file)):\n", "                missing_files.append(f'text_encoder_2/{file}')\n", "    else:\n", "        missing_files.extend([f'text_encoder_2/{file}' for file in required_files['text_encoder_2']])\n", "    \n", "    # 检查主模型文件\n", "    for file in required_files['main_model']:\n", "        if not os.path.exists(os.path.join(target_path, file)):\n", "            missing_files.append(file)\n", "    \n", "    # 如果没有缺失文件，说明模型完整\n", "    if not missing_files:\n", "        return True, target_path\n", "    \n", "    # 如果只是缺少少数文件，静默处理，不在这里显示\n", "    if len(missing_files) <= 3:\n", "        return False, None\n", "    \n", "    # 如果缺少大量文件，说明模型不完整\n", "    return False, None\n", "\n", "# 工具函数：清理缓存\n", "def clean_cache():\n", "    \"\"\"通用递归清理 autodl-tmp 目录下的缓存、临时文件和回收站\"\"\"\n", "    cleaned_size = 0\n", "    \n", "    # 1. 通用递归搜索并清理所有回收站目录\n", "    try:\n", "        trash_search_cmd = r\"\"\"find /root/autodl-tmp/ -type d \\( \\\n", "            -name '.Trash-*' -o -name '.trash' -o -name 'Trash' -o \\\n", "            -path '*/.local/share/Trash' \\\n", "        \\) 2>/dev/null\"\"\"\n", "        \n", "        result = subprocess.run(['bash', '-c', trash_search_cmd], \n", "                              stdout=subprocess.PIPE, text=True)\n", "        if result.stdout.strip():\n", "            for trash_path in result.stdout.strip().split('\\n'):\n", "                if trash_path and os.path.exists(trash_path):\n", "                    try:\n", "                        size_result = subprocess.run(['du', '-s', trash_path], \n", "                                                   stdout=subprocess.PIPE, text=True)\n", "                        if size_result.returncode == 0:\n", "                            size_kb = int(size_result.stdout.split()[0])\n", "                            cleaned_size += size_kb / (1024 * 1024)\n", "                        \n", "                        subprocess.run(['rm', '-rf', trash_path], \n", "                                     stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)\n", "                    except:\n", "                        pass\n", "    except:\n", "        pass\n", "    \n", "    # 2. 通用递归搜索并清理所有缓存目录\n", "    try:\n", "        cache_search_cmd = r\"\"\"find /root/autodl-tmp/ -type d \\( \\\n", "            -name '.cache' -o -name 'cache' -o -name 'tmp' -o -name '.tmp' -o \\\n", "            -name 'huggingface' -o -name 'hub' -o -name '.huggingface' \\\n", "        \\) 2>/dev/null\"\"\"\n", "        \n", "        result = subprocess.run(['bash', '-c', cache_search_cmd], \n", "                              stdout=subprocess.PIPE, text=True)\n", "        if result.stdout.strip():\n", "            for cache_path in result.stdout.strip().split('\\n'):\n", "                if cache_path and os.path.exists(cache_path):\n", "                    try:\n", "                        size_result = subprocess.run(['du', '-s', cache_path], \n", "                                                   stdout=subprocess.PIPE, text=True)\n", "                        if size_result.returncode == 0:\n", "                            size_kb = int(size_result.stdout.split()[0])\n", "                            cleaned_size += size_kb / (1024 * 1024)\n", "                        \n", "                        subprocess.run(['rm', '-rf', cache_path], \n", "                                     stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)\n", "                    except:\n", "                        pass\n", "    except:\n", "        pass\n", "    \n", "    # 3. 通用递归搜索并清理所有临时文件\n", "    try:\n", "        temp_files_cmd = r\"\"\"find /root/autodl-tmp/ \\( \\\n", "            -name '*.tmp' -o -name '*.part' -o -name '*.download' -o \\\n", "            -name '.git*' -o -name '.*~' -o -name '*~' -o \\\n", "            -name '.DS_Store' -o -name '*.bak' -o -name '*.old' -o \\\n", "            -name '*.swp' -o -name '*.lock' -o -name '*.temp' -o \\\n", "            -name '*.log' -o -name '*.pid' -o -name 'core.*' \\\n", "        \\) -type f 2>/dev/null\"\"\"\n", "        \n", "        result = subprocess.run(['bash', '-c', temp_files_cmd], \n", "                              stdout=subprocess.PIPE, text=True)\n", "        if result.stdout.strip():\n", "            for file_path in result.stdout.strip().split('\\n'):\n", "                if file_path and os.path.exists(file_path):\n", "                    try:\n", "                        size = os.path.getsize(file_path)\n", "                        cleaned_size += size / (1024 * 1024 * 1024)\n", "                        os.remove(file_path)\n", "                    except:\n", "                        pass\n", "    except:\n", "        pass\n", "    \n", "    # 4. 智能清理下载临时目录（仅包含非模型文件的目录）\n", "    try:\n", "        temp_dir_patterns = ['*-train', '*-dev', 'zealman', 'download*']\n", "        \n", "        for pattern in temp_dir_patterns:\n", "            find_cmd = f\"find /root/autodl-tmp/ -type d -name '{pattern}' 2>/dev/null\"\n", "            result = subprocess.run(['bash', '-c', find_cmd], \n", "                                  stdout=subprocess.PIPE, text=True)\n", "            \n", "            if result.stdout.strip():\n", "                for temp_dir in result.stdout.strip().split('\\n'):\n", "                    if temp_dir and os.path.exists(temp_dir):\n", "                        try:\n", "                            # 检查是否只包含非模型文件\n", "                            model_files_cmd = f\"find '{temp_dir}' -name '*.safetensors' -o -name '*.bin' -o -name '*.pth' 2>/dev/null\"\n", "                            model_result = subprocess.run(['bash', '-c', model_files_cmd], \n", "                                                        stdout=subprocess.PIPE, text=True)\n", "                            \n", "                            # 如果没有模型文件，可以安全删除\n", "                            if not model_result.stdout.strip():\n", "                                size_result = subprocess.run(['du', '-s', temp_dir], \n", "                                                           stdout=subprocess.PIPE, text=True)\n", "                                if size_result.returncode == 0:\n", "                                    size_kb = int(size_result.stdout.split()[0])\n", "                                    cleaned_size += size_kb / (1024 * 1024)\n", "                                \n", "                                subprocess.run(['rm', '-rf', temp_dir], \n", "                                             stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)\n", "                        except:\n", "                            pass\n", "    except:\n", "        pass\n", "\n", "    # 5. 清理空目录（多次清理，因为删除文件后可能产生新的空目录）\n", "    try:\n", "        for _ in range(3):\n", "            empty_dirs_cmd = \"find /root/autodl-tmp/ -type d -empty -not -path '/root/autodl-tmp' 2>/dev/null\"\n", "            result = subprocess.run(['bash', '-c', empty_dirs_cmd], \n", "                                  stdout=subprocess.PIPE, text=True)\n", "            if result.stdout.strip():\n", "                for dir_path in result.stdout.strip().split('\\n'):\n", "                    if dir_path and os.path.exists(dir_path):\n", "                        try:\n", "                            os.rmdir(dir_path)\n", "                        except:\n", "                            pass\n", "            else:\n", "                break  # 没有更多空目录了\n", "    except:\n", "        pass\n", "    \n", "    return cleaned_size\n", "\n", "# 深度清理函数（用于严重空间不足时）\n", "def deep_clean():\n", "    \"\"\"深度清理 autodl-tmp 目录，包括所有可能的垃圾文件\"\"\"\n", "    cleaned_size = 0\n", "    \n", "    try:\n", "        cleaned_size += clean_cache()\n", "        \n", "        # 清理大型日志文件\n", "        log_files_cmd = \"find /root/autodl-tmp/ -name '*.log' -size +10M -exec rm -f {} + 2>/dev/null\"\n", "        subprocess.run(['bash', '-c', log_files_cmd], \n", "                      stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)\n", "        \n", "        # 强制同步\n", "        subprocess.run(['sync'], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)\n", "        \n", "    except:\n", "        pass\n", "    \n", "    return cleaned_size\n", "\n", "# 创建输出区域\n", "output = widgets.Output()\n", "status_label = widgets.HTML(value='')\n", "\n", "# 全局变量跟踪服务状态\n", "service_running = False\n", "service_thread = None\n", "\n", "\n", "\n", "# 一键启动函数（合并下载模型和启动UI）\n", "def one_click_start(btn):\n", "    global service_running, service_thread\n", "    \n", "    btn.disabled = True\n", "    btn_clean.disabled = True\n", "    status_label.value = '<span class=\"status-indicator status-running\"></span>正在检查模型...'\n", "    \n", "    with output:\n", "        output.clear_output()\n", "        \n", "        # 步骤1: 检查磁盘空间\n", "        print(\"💽 正在检查磁盘空间...\")\n", "        status_label.value = '<span class=\"status-indicator status-running\"></span>检查磁盘空间...'\n", "        time.sleep(1)\n", "        \n", "        available_space = check_disk_space()\n", "        \n", "        output.clear_output()\n", "        print(f\"💽 当前可用空间: {available_space:.1f}GB\")\n", "        \n", "        # 检查空间是否足够（需要至少57GB）\n", "        if available_space < 57:\n", "            print(\"❌ 磁盘空间不足！\")\n", "            print(f\"📊 当前可用: {available_space:.1f}GB\")\n", "            print(\"📊 最少需要: 57GB\")\n", "            print(\"💡 请扩充数据盘10GB空间后再试\")\n", "            status_label.value = '<span class=\"status-indicator status-error\"></span>磁盘空间不足'\n", "            btn.disabled = False\n", "            btn_clean.disabled = False\n", "            return\n", "        \n", "        print(\"✅ 磁盘空间充足\")\n", "        time.sleep(1)\n", "        \n", "        # 步骤2: 检查模型是否已存在\n", "        output.clear_output()\n", "        print(f\"💽 当前可用空间: {available_space:.1f}GB ✅\")\n", "        print(\"🔍 正在检查模型文件...\")\n", "        status_label.value = '<span class=\"status-indicator status-running\"></span>检查模型文件...'\n", "        time.sleep(1)\n", "        \n", "        models_exist, model_path = check_models_exist()\n", "        \n", "        # 清除上一步信息，显示检查结果\n", "        output.clear_output()\n", "        \n", "        if models_exist:\n", "            print(\"✅ 模型文件已存在，跳过下载\")\n", "            print(f\"📁 模型路径: {model_path}\")\n", "            status_label.value = '<span class=\"status-indicator status-running\"></span>准备启动UI...'\n", "            \n", "            time.sleep(1)\n", "            \n", "            # 检查服务是否已经运行\n", "            output.clear_output()\n", "            print(\"✅ 模型文件已存在\")\n", "            print(\"🔍 检查训练UI状态...\")\n", "            \n", "            # 清理可能的异常进程\n", "            subprocess.run(['bash', '-c', 'kill -9 $(lsof -ti:6006) 2>/dev/null'], \n", "                          stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)\n", "            time.sleep(2)\n", "            \n", "            # 启动服务\n", "            output.clear_output()\n", "            print(\"✅ 模型文件已存在，跳过下载\")\n", "            print(\"⏳ 正在启动训练UI...\")\n", "            status_label.value = '<span class=\"status-indicator status-running\"></span>正在启动UI...'\n", "            \n", "            def run_server():\n", "                global service_running\n", "                try:\n", "                    cmd = '''cd /root/ai-toolkit/ui && \n", "                             export NVM_DIR=\"$HOME/.nvm\" && \n", "                             [ -s \"$NVM_DIR/nvm.sh\" ] && . \"$NVM_DIR/nvm.sh\" && \n", "                             npm run start 2>/dev/null'''\n", "                    subprocess.Popen(cmd, shell=True, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)\n", "                    service_running = True\n", "                except:\n", "                    service_running = False\n", "                    \n", "            service_thread = threading.Thread(target=run_server)\n", "            service_thread.start()\n", "            \n", "            # 等待服务启动\n", "            time.sleep(5)\n", "            \n", "            output.clear_output()\n", "            print(\"✅ 模型文件已存在，跳过下载\")\n", "            print(\"✅ 训练UI启动成功！\")\n", "            print(\"🌐 请访问: http://localhost:6006\")\n", "            \n", "            btn_one_click.disabled = False\n", "            status_label.value = '<span class=\"status-indicator status-success\"></span>启动完成'\n", "            btn_clean.disabled = False\n", "            return\n", "\n", "        else:\n", "            print(\"📥 需要下载模型文件\")\n", "            time.sleep(1)\n", "        \n", "        # 空间已在开头检查过，直接开始下载\n", "        time.sleep(1)\n", "        \n", "        # 步骤3: 开始下载\n", "        output.clear_output()\n", "        print(\"📥 开始下载模型...\")\n", "        print(\"📋 第1步: 复制基础模型文件...\")\n", "        status_label.value = '<span class=\"status-indicator status-running\"></span>下载模型中...'\n", "        time.sleep(1)\n", "        \n", "        try:\n", "            result1 = subprocess.run(['/bin/bash', '/root/scripts-aitoolkit/copy_fluxkontext_model.sh'], \n", "                                    stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True, timeout=300)\n", "            \n", "            # 显示第1步结果\n", "            output.clear_output()\n", "            print(\"📥 开始下载模型...\")\n", "            \n", "            if result1.returncode == 0:\n", "                if result1.stdout and (\"已存在\" in result1.stdout or \"exist\" in result1.stdout.lower()):\n", "                    print(\"✓ 基础模型文件已存在\")\n", "                else:\n", "                    print(\"✓ 基础模型复制完成\")\n", "            else:\n", "                print(\"❌ 基础模型复制失败\")\n", "        \n", "        except subprocess.TimeoutExpired:\n", "            output.clear_output()\n", "            print(\"📥 开始下载模型...\")\n", "            print(\"⏰ 复制超时，清理后重试...\")\n", "            clean_cache()\n", "        \n", "        # 3.2: 下载补充组件\n", "        time.sleep(1)\n", "        \n", "        output.clear_output()\n", "        print(\"📥 开始下载模型...\")\n", "        print(\"✓ 基础模型处理完成\")\n", "        print(\"📋 第2步: 下载补充组件...\")\n", "        time.sleep(1)\n", "        \n", "        # 定义需要下载的6个文件\n", "        download_files = [\n", "            ('transformer', 'diffusion_pytorch_model-00001-of-00003.safetensors'),\n", "            ('transformer', 'diffusion_pytorch_model-00002-of-00003.safetensors'),\n", "            ('transformer', 'diffusion_pytorch_model-00003-of-00003.safetensors'),\n", "            ('text_encoder_2', 'model-00001-of-00002.safetensors'),\n", "            ('text_encoder_2', 'model-00002-of-00002.safetensors'),\n", "            ('flux1-kontext-dev', 'flux1-kontext-dev.safetensors')\n", "        ]\n", "        \n", "        total_files = len(download_files)\n", "        completed_files = 0\n", "        \n", "        try:\n", "            # 分别下载每个组件并显示进度\n", "            for i, (component_type, filename) in enumerate(download_files, 1):\n", "                output.clear_output()\n", "                print(\"📥 开始下载模型...\")\n", "                print(\"✓ 基础模型处理完成\")\n", "                print(\"📋 第2步: 下载补充组件...\")\n", "                # 简化文件名显示\n", "                display_name = filename.replace('diffusion_pytorch_model-', 'transformer-').replace('.safetensors', '')\n", "                if 'model-' in filename and 'text_encoder' in component_type:\n", "                    display_name = filename.replace('model-', 'text_encoder-').replace('.safetensors', '')\n", "                elif 'flux1-kontext-dev' in filename:\n", "                    display_name = 'flux1-kontext-dev'\n", "                \n", "                print(f\"📁 正在下载 ({i}/{total_files}): {display_name}\")\n", "                \n", "                # 检查文件是否已存在\n", "                if component_type == 'flux1-kontext-dev':\n", "                    file_path = f\"/root/autodl-tmp/black-forest-labs/FLUX.1-Kontext-dev/{filename}\"\n", "                else:\n", "                    file_path = f\"/root/autodl-tmp/black-forest-labs/FLUX.1-Kontext-dev/{component_type}/{filename}\"\n", "                \n", "                if os.path.exists(file_path):\n", "                    completed_files += 1\n", "                    output.clear_output()\n", "                    print(\"📥 开始下载模型...\")\n", "                    print(\"✓ 基础模型处理完成\")\n", "                    print(\"📋 第2步: 下载补充组件...\")\n", "                    \n", "                    # 计算并显示百分比\n", "                    file_progress = 100\n", "                    overall_progress = (completed_files / total_files) * 100\n", "                    print(f\"✓ ({i}/{total_files}) {file_progress}% - {display_name} (已存在)\")\n", "                    print(f\"📊 总体进度: {overall_progress:.0f}% ({completed_files}/{total_files} 已完成)\")\n", "                    \n", "                    # 更新状态\n", "                    status_label.value = f'<span class=\"status-indicator status-running\"></span>下载进度: {overall_progress:.0f}%'\n", "                    time.sleep(0.5)\n", "                    continue\n", "                \n", "                # 执行下载\n", "                status_label.value = f'<span class=\"status-indicator status-running\"></span>下载中: ({i}/{total_files})'\n", "                \n", "                try:\n", "                    if component_type == 'flux1-kontext-dev':\n", "                        result = subprocess.run(['/bin/bash', '/root/scripts-aitoolkit/down_blackfluxkontextmodel.sh', 'flux1-kontext-dev'], \n", "                                              stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True, timeout=180)\n", "                    else:\n", "                        result = subprocess.run(['/bin/bash', '/root/scripts-aitoolkit/down_blackfluxkontextmodel.sh', component_type], \n", "                                              stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True, timeout=180)\n", "                    \n", "                    if result.returncode == 0:\n", "                        completed_files += 1\n", "                        output.clear_output()\n", "                        print(\"📥 开始下载模型...\")\n", "                        print(\"✓ 基础模型处理完成\")\n", "                        print(\"📋 第2步: 下载补充组件...\")\n", "                        \n", "                        # 计算并显示百分比\n", "                        file_progress = 100\n", "                        overall_progress = (completed_files / total_files) * 100\n", "                        print(f\"✓ ({i}/{total_files}) {file_progress}% - {display_name}\")\n", "                        print(f\"📊 总体进度: {overall_progress:.0f}% ({completed_files}/{total_files} 已完成)\")\n", "                        \n", "                        status_label.value = f'<span class=\"status-indicator status-running\"></span>下载进度: {overall_progress:.0f}%'\n", "                        time.sleep(0.5)\n", "                    else:\n", "                        output.clear_output()\n", "                        print(\"📥 开始下载模型...\")\n", "                        print(\"✓ 基础模型处理完成\")\n", "                        print(\"📋 第2步: 下载补充组件...\")\n", "                        \n", "                        # 显示失败的百分比\n", "                        file_progress = 0\n", "                        print(f\"❌ ({i}/{total_files}) {file_progress}% - {display_name}\")\n", "                        time.sleep(0.5)\n", "                \n", "                except subprocess.TimeoutExpired:\n", "                    output.clear_output()\n", "                    print(\"📥 开始下载模型...\")\n", "                    print(\"✓ 基础模型处理完成\")\n", "                    print(\"📋 第2步: 下载补充组件...\")\n", "                    \n", "                    # 显示超时的百分比\n", "                    file_progress = 0\n", "                    print(f\"⏰ ({i}/{total_files}) {file_progress}% - {display_name} (超时)\")\n", "                    time.sleep(0.5)\n", "            \n", "            # 显示最终结果\n", "            output.clear_output()\n", "            print(\"📥 开始下载模型...\")\n", "            print(\"✓ 基础模型处理完成\")\n", "            \n", "            if completed_files == total_files:\n", "                print(f\"✅ 补充组件下载完成 ({completed_files}/{total_files})\")\n", "            elif completed_files > 0:\n", "                print(f\"⚠️ 部分组件下载完成 ({completed_files}/{total_files})\")\n", "            else:\n", "                print(\"❌ 补充组件下载失败\")\n", "        \n", "        except subprocess.TimeoutExpired:\n", "            output.clear_output()\n", "            print(\"⏰ 下载超时，请检查网络\")\n", "            status_label.value = '<span class=\"status-indicator status-error\"></span>下载超时'\n", "            btn_one_click.disabled = False\n", "            btn_clean.disabled = False\n", "            return\n", "        except Exception as e:\n", "            output.clear_output()\n", "            print(f\"❌ 下载异常: {str(e)}\")\n", "            status_label.value = '<span class=\"status-indicator status-error\"></span>下载失败'\n", "            btn_one_click.disabled = False\n", "            btn_clean.disabled = False\n", "            return\n", "        \n", "        # 步骤4: 模型下载完成，开始启动UI\n", "        time.sleep(1)\n", "        \n", "        output.clear_output()\n", "        print(\"✅ 模型下载完成！\")\n", "        \n", "        # 步骤6: 启动训练UI\n", "        # 清理可能的异常进程\n", "        subprocess.run(['bash', '-c', 'kill -9 $(lsof -ti:6006) 2>/dev/null'], \n", "                      stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)\n", "        time.sleep(2)\n", "        \n", "        # 启动服务\n", "        output.clear_output()\n", "        print(\"✅ 模型下载完成！\")\n", "        print(\"⏳ 正在启动训练UI...\")\n", "        status_label.value = '<span class=\"status-indicator status-running\"></span>正在启动UI...'\n", "        \n", "        def run_server():\n", "            global service_running\n", "            try:\n", "                cmd = '''cd /root/ai-toolkit/ui && \n", "                         export NVM_DIR=\"$HOME/.nvm\" && \n", "                         [ -s \"$NVM_DIR/nvm.sh\" ] && . \"$NVM_DIR/nvm.sh\" && \n", "                         npm run start 2>/dev/null'''\n", "                subprocess.Popen(cmd, shell=True, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)\n", "                service_running = True\n", "            except:\n", "                service_running = False\n", "                \n", "        service_thread = threading.Thread(target=run_server)\n", "        service_thread.start()\n", "        \n", "        # 等待服务启动\n", "        time.sleep(5)\n", "        \n", "        output.clear_output()\n", "        print(\"✅ 模型下载完成！\")\n", "        print(\"✅ 训练UI启动成功！\")\n", "        print(\"🌐 请访问: http://localhost:6006\")\n", "        \n", "\n", "        \n", "        btn_one_click.disabled = False\n", "        status_label.value = '<span class=\"status-indicator status-success\"></span>启动完成'\n", "        \n", "    btn_clean.disabled = False\n", "\n", "\n", "\n", "\n", "\n", "# 手动清理缓存函数\n", "def manual_clean_cache(btn):\n", "    btn.disabled = True\n", "    with output:\n", "        output.clear_output()\n", "        \n", "        # 步骤1: 检查当前空间\n", "        print(\"📊 正在检查当前磁盘空间...\")\n", "        time.sleep(1)\n", "        \n", "        space_before = check_disk_space()\n", "        \n", "        # 清除上一步，显示空间信息\n", "        output.clear_output()\n", "        print(f\"📊 当前可用空间: {space_before:.1f}GB\")\n", "        time.sleep(1)\n", "        \n", "        # 步骤2: 开始清理\n", "        output.clear_output()\n", "        print(f\"📊 当前可用空间: {space_before:.1f}GB\")\n", "        print(\"🗑️ 正在清理临时文件...\")\n", "        time.sleep(1)\n", "        \n", "        cleaned_size = clean_cache()\n", "        space_after = check_disk_space()\n", "        \n", "        # 步骤3: 显示最终结果\n", "        output.clear_output()\n", "        \n", "        if cleaned_size > 0:\n", "            print(f\"✅ 清理完成！\")\n", "            print(f\"📊 释放空间: {cleaned_size:.1f}GB\")\n", "            print(f\"📊 现在可用: {space_after:.1f}GB\")\n", "        else:\n", "            print(\"ℹ️ 没有发现可清理的文件\")\n", "            print(f\"📊 当前可用: {space_after:.1f}GB\")\n", "        \n", "        print(\"💡 已清理: 所有回收站、缓存、临时文件、下载临时目录、空目录（智能递归搜索）\")\n", "    \n", "    btn.disabled = False\n", "\n", "# 创建按钮\n", "btn_one_click = widgets.Button(\n", "    description='一键启动',\n", "    _dom_classes=['apple-btn'],\n", "    layout=widgets.Layout(width='auto', height='40px')\n", ")\n", "\n", "btn_clean = widgets.Button(\n", "    description='清理缓存',\n", "    _dom_classes=['apple-btn'],\n", "    layout=widgets.Layout(width='auto', height='40px')\n", ")\n", "\n", "\n", "# 绑定事件\n", "btn_one_click.on_click(one_click_start)\n", "btn_clean.on_click(manual_clean_cache)\n", "\n", "# 页面加载时初始化\n", "def initialize_ui_state():\n", "    \"\"\"页面加载时初始化UI状态\"\"\"\n", "    pass\n", "\n", "\n", "\n", "# 创建布局\n", "button_box = widgets.HBox(\n", "    [btn_one_click, btn_clean],\n", "    layout=widgets.Layout(\n", "        gap='16px', \n", "        margin='20px 0',\n", "        display='flex',\n", "        flex_flow='row',\n", "        align_items='center',\n", "        justify_content='flex-start'\n", "    )\n", ")\n", "\n", "# 显示界面\n", "display(widgets.VBox([\n", "    status_label,\n", "    button_box,\n", "    output\n", "], layout=widgets.Layout(width='100%', margin='0', padding='0')))\n", "\n", "# 初始化UI\n", "initialize_ui_state()\n", "\n", "# 隐藏所有代码单元格的输入区域\n", "display(Javascript(\"\"\"\n", "setTimeout(function() {\n", "    document.querySelectorAll('.jp-CodeCell .jp-InputArea').forEach(function(el) {\n", "        el.style.display = 'none';\n", "    });\n", "}, 100);\n", "\"\"\"))\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 4}