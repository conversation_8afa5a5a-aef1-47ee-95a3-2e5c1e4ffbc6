from IPython.display import HTML, Javascript, display, clear_output
import ipywidgets as widgets
import subprocess
import threading
import time
import os

# 清理之前的输出
clear_output()

# 注入苹果风格 CSS
display(HTML("""
<style>
/* 苹果风格按钮 */
.apple-btn {
    background: linear-gradient(180deg, #0A84FF 0%, #0060DF 100%);
    color: #FFFFFF !important;
    border: none;
    border-radius: 10px;
    padding: 12px 24px;
    font-size: 15px;
    font-weight: 500;
    box-shadow: 0 3px 8px rgba(0,0,0,0.15);
    transition: all 0.2s ease;
    min-width: 160px;
    display: inline-block !important;
    height: auto !important;
    line-height: normal !important;
}
.apple-btn:hover {
    background: linear-gradient(180deg, #379AFF 0%, #007AFF 100%);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    transform: translateY(-1px);
}
.apple-btn:active {
    transform: scale(0.98);
    box-shadow: 0 2px 4px rgba(0,0,0,0.25);
}
.apple-btn:disabled {
    background: #E5E5EA !important;
    color: #8E8E93 !important;
    box-shadow: none !important;
    cursor: not-allowed !important;
    opacity: 0.6 !important;
}

/* 运行中状态按钮 */
.apple-btn-running {
    background: linear-gradient(180deg, #34C759 0%, #30B350 100%) !important;
    color: #FFFFFF !important;
    border: none;
    border-radius: 10px;
    padding: 12px 24px;
    font-size: 15px;
    font-weight: 500;
    box-shadow: 0 3px 8px rgba(52,199,89,0.3);
    transition: all 0.2s ease;
    min-width: 160px;
    display: inline-block !important;
    height: auto !important;
    line-height: normal !important;
}
.apple-btn-running:hover {
    background: linear-gradient(180deg, #40D368 0%, #34C759 100%) !important;
    box-shadow: 0 4px 12px rgba(52,199,89,0.4);
    transform: translateY(-1px);
}
.apple-btn-running:active {
    transform: scale(0.98);
    box-shadow: 0 2px 4px rgba(52,199,89,0.5);
}

/* 停止按钮样式 */
.apple-btn-stop {
    background: linear-gradient(180deg, #FF3B30 0%, #D70015 100%) !important;
    color: #FFFFFF !important;
    border: none;
    border-radius: 10px;
    padding: 12px 24px;
    font-size: 15px;
    font-weight: 500;
    box-shadow: 0 3px 8px rgba(255,59,48,0.3);
    transition: all 0.2s ease;
    min-width: 160px;
    display: inline-block !important;
    height: auto !important;
    line-height: normal !important;
}
.apple-btn-stop:hover {
    background: linear-gradient(180deg, #FF453A 0%, #FF3B30 100%) !important;
    box-shadow: 0 4px 12px rgba(255,59,48,0.4);
    transform: translateY(-1px);
}
.apple-btn-stop:active {
    transform: scale(0.98);
    box-shadow: 0 2px 4px rgba(255,59,48,0.5);
}

/* JupyterLab 特定的禁用状态样式 */
.widget-button[disabled] {
    background: #E5E5EA !important;
    color: #8E8E93 !important;
    opacity: 0.6 !important;
    pointer-events: none !important;
}

/* 修复 Jupyter 按钮样式 */
.widget-button {
    overflow: visible !important;
}
.jupyter-widgets.widget-button .widget-button {
    height: auto !important;
}

/* 输出区域样式 */
.widget-output {
    background: #F2F2F7;
    border-radius: 8px;
    padding: 12px 16px;
    margin-top: 12px;
}
.widget-output .output_area {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
    font-size: 13px;
    color: #1C1C1E;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
    animation: pulse 2s infinite;
}
.status-success { background: #34C759; }
.status-running { background: #FF9500; }
.status-error { background: #FF3B30; }

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* 修复整体布局 */
.widget-vbox {
    gap: 0 !important;
}
.jp-OutputArea-output {
    overflow: visible !important;
}
</style>
"""))

# 工具函数：检查模型下载脚本状态
def check_download_script():
    """检查下载脚本是否存在"""
    script_path = '/root/scripts-aitoolkit/down_blackfluxkontextmodel.sh'
    return os.path.exists(script_path)

# 工具函数：运行模型下载脚本
def run_download_script():
    """运行模型下载脚本，返回(成功状态, 输出信息)"""
    try:
        result = subprocess.run(
            ['/bin/bash', '/root/scripts-aitoolkit/down_blackfluxkontextmodel.sh', 'all'],
            stdout=subprocess.PIPE, 
            stderr=subprocess.STDOUT, 
            text=True, 
            timeout=1800  # 30分钟超时
        )
        return result.returncode == 0, result.stdout
    except subprocess.TimeoutExpired:
        return False, "下载超时（30分钟）"
    except Exception as e:
        return False, f"执行异常: {str(e)}"

# 工具函数：运行模型下载脚本并显示进度
def run_download_script_with_progress():
    """运行模型下载脚本并实时显示进度"""
    try:
        # 初始化进度显示
        progress_lines = [
            "1/6  检查中... transformer-1",
            "2/6  检查中... transformer-2", 
            "3/6  检查中... transformer-3",
            "4/6  检查中... text_encoder-1",
            "5/6  检查中... text_encoder-2",
            "6/6  检查中... flux1-kontext-dev"
        ]
        
        def update_progress_display():
            print("📊 模型下载进度:")
            for line in progress_lines:
                print(f"   {line}")
        
        # 启动子进程
        process = subprocess.Popen(
            ['/bin/bash', '/root/scripts-aitoolkit/down_blackfluxkontextmodel.sh', 'all'],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        output_lines = []
        
        # 实时读取输出
        while True:
            line = process.stdout.readline()
            if not line and process.poll() is not None:
                break
            
            if line:
                output_lines.append(line.strip())
                
                # 检查是否是进度信息
                if line.startswith('PROGRESS:'):
                    try:
                        # 解析进度信息: PROGRESS:1/6:100%:transformer-1
                        parts = line.strip().split(':')
                        if len(parts) >= 4:
                            file_index = parts[1]
                            status = parts[2]
                            file_name = parts[3]
                            
                            # 更新对应的进度行
                            index = int(file_index.split('/')[0]) - 1
                            if 0 <= index < len(progress_lines):
                                if status == 'checking':
                                    progress_lines[index] = f"{file_index}  检查中... {file_name}"
                                elif status == 'downloading':
                                    progress_lines[index] = f"{file_index}  下载中... {file_name}"
                                elif status == '100%':
                                    progress_lines[index] = f"{file_index}  ✅ 100% {file_name}"
                                elif status == 'failed':
                                    progress_lines[index] = f"{file_index}  ❌ 失败 {file_name}"
                                
                                # 更新显示
                                output.clear_output()
                                print("📥 模型管理脚本运行中...")
                                print("⏳ 请耐心等待，这可能需要几分钟到半小时")
                                print("📋 正在执行：磁盘空间检查、垃圾文件清理、模型下载和验证")
                                print("")
                                update_progress_display()
                    except:
                        pass  # 忽略解析错误
        
        # 等待进程完成
        return_code = process.wait()
        
        return return_code == 0, '\n'.join(output_lines)
        
    except Exception as e:
        return False, f"执行异常: {str(e)}"

# 工具函数：检测端口状态
def check_port_status(port=6006):
    """检测指定端口是否有服务在运行"""
    try:
        result = subprocess.run(['bash', '-c', f'lsof -ti:{port}'], 
                              stdout=subprocess.PIPE, stderr=subprocess.DEVNULL, text=True)
        return result.returncode == 0 and result.stdout.strip() != ''
    except:
        return False

# 工具函数：启动训练UI
def start_training_ui():
    """启动训练UI服务"""
    try:
        # 清理可能的异常进程
        subprocess.run(['bash', '-c', 'kill -9 $(lsof -ti:6006) 2>/dev/null'], 
                      stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        time.sleep(2)
        
        # 启动服务
        cmd = '''cd /root/ai-toolkit/ui && 
                 export NVM_DIR="$HOME/.nvm" && 
                 [ -s "$NVM_DIR/nvm.sh" ] && . "$NVM_DIR/nvm.sh" && 
                 npm run start 2>/dev/null'''
        subprocess.Popen(cmd, shell=True, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        return True
    except:
        return False

# 工具函数：停止训练UI服务
def stop_training_ui():
    """停止训练UI服务"""
    try:
        result = subprocess.run(['bash', '-c', 'kill -9 $(lsof -ti:6006) 2>/dev/null'], 
                              stdout=subprocess.PIPE, stderr=subprocess.DEVNULL, text=True)
        time.sleep(2)
        return not check_port_status(6006)  # 返回是否成功停止
    except:
        return False

# 工具函数：设置按钮为运行状态
def set_button_running_state():
    """设置按钮为运行中状态"""
    btn_one_click.description = '运行中'
    btn_one_click._dom_classes = ['apple-btn-running']
    btn_one_click.disabled = False
    status_label.value = '<span class="status-indicator status-success"></span>训练UI运行中'
    btn_stop.style.display = 'block'  # 显示停止按钮

# 工具函数：设置按钮为正常状态
def set_button_normal_state():
    """设置按钮为正常状态"""
    btn_one_click.description = '一键启动'
    btn_one_click._dom_classes = ['apple-btn']
    btn_one_click.disabled = False
    status_label.value = ''
    btn_stop.style.display = 'none'  # 隐藏停止按钮

# 工具函数：检查文件存储目录
def check_file_storage():
    """检查文件存储目录是否可用"""
    storage_path = '/root/autodl-fs'
    try:
        # 检查是否是真实的目录（不是文件）
        if os.path.isdir(storage_path):
            # 尝试在目录中创建测试文件来验证可写性
            test_file = os.path.join(storage_path, '.test_write')
            try:
                with open(test_file, 'w') as f:
                    f.write('test')
                os.remove(test_file)
                return True, '文件存储目录可用'
            except:
                return False, '文件存储目录存在但不可写'
        else:
            return False, '文件存储目录不存在或不是目录'
    except:
        return False, '无法访问文件存储目录'

# 工具函数：复制训练结果
def copy_training_results(target_path):
    """复制训练结果到指定路径"""
    try:
        # 确保目标目录存在
        os.makedirs(target_path, exist_ok=True)
        
        # 执行复制脚本
        copy_script = f'''#!/bin/bash
for dir in /root/output/*; do
    if [ -d "$dir" ]; then
        base=$(basename "$dir")
        target="{target_path}/$base"
        if [ -e "$target" ]; then
            timestamp=$(date +%Y%m%d%H%M%S)
            target="${{target}}_$timestamp"
        fi
        echo "正在复制: $base -> $(basename "$target")"
        rsync -a --info=progress2 "$dir" "$target"
        echo "复制完成: $(basename "$target")"
    fi
done
'''
        
        result = subprocess.run(
            ['bash', '-c', copy_script],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            timeout=600  # 10分钟超时
        )
        
        return result.returncode == 0, result.stdout
    except subprocess.TimeoutExpired:
        return False, "复制超时（10分钟）"
    except Exception as e:
        return False, f"复制异常: {str(e)}"

# 创建输出区域
output = widgets.Output()
status_label = widgets.HTML(value='')

# 全局变量跟踪服务状态
service_running = False
service_thread = None



# 一键启动函数（简化版）
def one_click_start(btn):
    global service_running, service_thread
    
    # 如果按钮是运行中状态，检查服务状态
    if btn.description == '运行中':
        with output:
            output.clear_output()
            print("🔍 正在检查训练UI状态...")
            
            if check_port_status(6006):
                print("✅ 训练UI正在运行中")
                print("🌐 请访问: http://localhost:6006")
                print("💡 如需重启服务，请先停止当前服务")
            else:
                print("⚠️ 检测到服务已停止，恢复启动按钮")
                set_button_normal_state()
        return
    
    btn.disabled = True
    btn_clean.disabled = True
    status_label.value = '<span class="status-indicator status-running"></span>正在准备模型...'
    
    with output:
        output.clear_output()
        
        # 步骤0: 检查端口状态
        if check_port_status(6006):
            print("✅ 检测到训练UI已在运行")
            print("🌐 请访问: http://localhost:6006")
            set_button_running_state()
            btn_clean.disabled = False
            return
        
        # 步骤1: 检查下载脚本
        print("🔍 正在检查系统环境...")
        if not check_download_script():
            print("❌ 下载脚本不存在！")
            print("📁 请确保 /root/scripts-aitoolkit/down_blackfluxkontextmodel.sh 文件存在")
            status_label.value = '<span class="status-indicator status-error"></span>系统环境异常'
            btn.disabled = False
            btn_clean.disabled = False
            return
        
        print("✅ 系统环境正常")
        time.sleep(1)
        
        # 步骤2: 运行模型下载脚本（包含空间检查、清理、下载、验证）
        output.clear_output()
        print("📥 正在运行模型管理脚本...")
        print("💡 脚本将自动执行：磁盘检查 → 垃圾清理 → 模型下载 → 完整性验证")
        status_label.value = '<span class="status-indicator status-running"></span>运行模型管理脚本...'
        time.sleep(2)
        
        # 显示实时进度
        output.clear_output()
        print("📥 模型管理脚本运行中...")
        print("⏳ 请耐心等待，这可能需要几分钟到半小时")
        print("📋 正在执行：磁盘空间检查、垃圾文件清理、模型下载和验证")
        print("")
        print("📊 模型下载进度:")
        
        # 运行下载脚本并实时显示进度
        success, script_output = run_download_script_with_progress()
        
        # 显示脚本执行结果
        output.clear_output()
        
        if success:
            print("✅ 模型管理脚本执行成功！")
            print("📋 所有模型文件已准备就绪")
            
            # 显示部分脚本输出（最后几行）
            if script_output:
                lines = script_output.strip().split('\n')
                if len(lines) > 3:
                    print("📄 脚本执行摘要:")
                    for line in lines[-3:]:
                        if line.strip():
                            print(f"   {line}")
            
            time.sleep(2)
            
            # 启动训练UI
            output.clear_output()
            print("✅ 模型文件准备完成")
            print("⏳ 正在启动训练UI...")
            status_label.value = '<span class="status-indicator status-running"></span>正在启动UI...'
            
            def run_server():
                global service_running
                service_running = start_training_ui()
                    
            service_thread = threading.Thread(target=run_server)
            service_thread.start()
            
            # 等待服务启动
            time.sleep(5)
            
            output.clear_output()
            print("✅ 模型文件准备完成")
            print("✅ 训练UI启动成功！")
            print("🌐 请访问: http://localhost:6006")
            
            # 设置按钮为运行中状态
            set_button_running_state()
            btn_clean.disabled = False
            return
        
        else:
            print("❌ 模型管理脚本执行失败！")
            
            # 显示错误信息
            if script_output:
                lines = script_output.strip().split('\n')
                print("📄 错误详情:")
                for line in lines[-5:]:  # 显示最后5行
                    if line.strip():
                        print(f"   {line}")
            
            status_label.value = '<span class="status-indicator status-error"></span>模型准备失败'
            btn.disabled = False
            btn_clean.disabled = False
            return

    btn_clean.disabled = False

# 停止服务函数
def stop_service(btn):
    global service_running, service_thread
    
    btn.disabled = True
    
    with output:
        output.clear_output()
        print("🛑 正在停止训练UI服务...")
        
        if stop_training_ui():
            print("✅ 训练UI服务已停止")
            set_button_normal_state()
            service_running = False
        else:
            print("⚠️ 停止服务时出现问题，请手动检查")
            # 仍然重置按钮状态，因为可能服务已经停止
            if not check_port_status(6006):
                set_button_normal_state()
                service_running = False
    
    btn.disabled = False


# 复制训练结果函数
def copy_results(btn):
    btn.disabled = True
    btn_one_click.disabled = True
    
    with output:
        output.clear_output()
        
        # 获取目标路径
        target_path = storage_path_input.value.strip()
        if not target_path:
            print("❌ 请输入文件存储路径")
            btn.disabled = False
            btn_one_click.disabled = False
            return
        
        # 检查文件存储目录
        print("🔍 正在检查文件存储目录...")
        storage_available, storage_msg = check_file_storage()
        
        if not storage_available:
            print(f"❌ {storage_msg}")
            print("")
            print("💡 解决方法:")
            print("   1. 到控制台左侧的'文件存储'开通机器所在区域的文件存储功能")
            print("   2. 初始化文件存储")
            print("   3. 重新启动机器")
            print("   4. 确保 /root/autodl-fs 是一个可访问的目录")
            btn.disabled = False
            btn_one_click.disabled = False
            return
        
        print(f"✅ {storage_msg}")
        time.sleep(1)
        
        # 检查训练结果目录
        output.clear_output()
        print(f"✅ 文件存储目录可用")
        print("🔍 正在检查训练结果...")
        
        if not os.path.exists('/root/output'):
            print("❌ 训练结果目录不存在: /root/output")
            print("💡 请先完成模型训练后再复制结果")
            btn.disabled = False
            btn_one_click.disabled = False
            return
        
        # 检查是否有训练结果
        try:
            result_dirs = [d for d in os.listdir('/root/output') 
                          if os.path.isdir(os.path.join('/root/output', d))]
            if not result_dirs:
                print("❌ 训练结果目录为空")
                print("💡 请先完成模型训练后再复制结果")
                btn.disabled = False
                btn_one_click.disabled = False
                return
        except:
            print("❌ 无法读取训练结果目录")
            btn.disabled = False
            btn_one_click.disabled = False
            return
        
        print(f"✅ 发现 {len(result_dirs)} 个训练结果目录")
        time.sleep(1)
        
        # 开始复制
        output.clear_output()
        print(f"✅ 文件存储目录可用")
        print(f"✅ 发现 {len(result_dirs)} 个训练结果目录")
        print("📁 正在复制训练结果...")
        print(f"📍 目标路径: {target_path}")
        print("")
        
        # 执行复制
        success, copy_output = copy_training_results(target_path)
        
        output.clear_output()
        
        if success:
            print("✅ 训练结果复制完成！")
            print(f"📍 目标路径: {target_path}")
            
            # 显示复制详情
            if copy_output:
                lines = copy_output.strip().split('\n')
                print("")
                print("📄 复制详情:")
                for line in lines:
                    if line.strip() and ('正在复制:' in line or '复制完成:' in line):
                        print(f"   {line}")
        else:
            print("❌ 训练结果复制失败！")
            if copy_output:
                lines = copy_output.strip().split('\n')
                print("")
                print("📄 错误详情:")
                for line in lines[-5:]:  # 显示最后5行
                    if line.strip():
                        print(f"   {line}")
    
    btn.disabled = False
    btn_one_click.disabled = False

# 手动清理缓存函数（简化版）
def manual_clean_cache(btn):
    btn.disabled = True
    with output:
        output.clear_output()
        
        print("🗑️ 正在运行垃圾文件清理...")
        print("💡 将清理：.downloading文件、临时文件、缓存目录、回收站等")
        time.sleep(1)
        
        try:
            # 运行清理脚本（只执行清理部分）
            result = subprocess.run(
                ['/bin/bash', '-c', 
                 'source /root/scripts-aitoolkit/down_blackfluxkontextmodel.sh && clean_cache'],
                stdout=subprocess.PIPE, 
                stderr=subprocess.STDOUT, 
                text=True, 
                timeout=300
            )
            
            output.clear_output()
            
            if result.returncode == 0:
                print("✅ 垃圾文件清理完成！")
                
                # 显示清理结果
                if result.stdout:
                    lines = result.stdout.strip().split('\n')
                    for line in lines[-3:]:  # 显示最后3行
                        if line.strip():
                            print(f"   {line}")
            else:
                print("⚠️ 清理过程中出现一些问题，但已尽力清理")
                
        except subprocess.TimeoutExpired:
            print("⏰ 清理超时，但部分文件可能已被清理")
        except Exception as e:
            print(f"❌ 清理异常: {str(e)}")
        
        print("💡 清理范围: /root/autodl-tmp/ 下的所有垃圾文件")
    
    btn.disabled = False

# 创建按钮
btn_one_click = widgets.Button(
    description='一键启动',
    _dom_classes=['apple-btn'],
    layout=widgets.Layout(width='auto', height='40px')
)

btn_stop = widgets.Button(
    description='停止服务',
    _dom_classes=['apple-btn-stop'],
    layout=widgets.Layout(width='auto', height='40px', display='none')
)

btn_clean = widgets.Button(
    description='清理缓存',
    _dom_classes=['apple-btn'],
    layout=widgets.Layout(width='auto', height='40px')
)

# 文件存储路径输入框
storage_path_input = widgets.Text(
    value='/root/autodl-fs/models/loras/kontext_lora/',
    placeholder='请输入文件存储路径',
    description='存储路径:',
    layout=widgets.Layout(width='400px', height='40px'),
    style={'description_width': '80px'}
)

# 复制训练结果按钮
btn_copy = widgets.Button(
    description='复制训练结果到文件存储',
    _dom_classes=['apple-btn'],
    layout=widgets.Layout(width='auto', height='40px')
)


# 绑定事件
btn_one_click.on_click(one_click_start)
btn_stop.on_click(stop_service)
btn_clean.on_click(manual_clean_cache)
btn_copy.on_click(copy_results)

# 页面加载时初始化
def initialize_ui_state():
    """页面加载时初始化UI状态"""
    # 检查端口状态，如果服务正在运行则设置按钮为运行状态
    if check_port_status(6006):
        set_button_running_state()
        with output:
            output.clear_output()
            print("✅ 检测到训练UI正在运行")
            print("🌐 请访问: http://localhost:6006")
            print("💡 点击'运行中'按钮可查看服务状态")
    else:
        set_button_normal_state()



# 创建布局
button_box = widgets.HBox(
    [btn_one_click, btn_stop, btn_clean],
    layout=widgets.Layout(
        gap='16px', 
        margin='20px 0',
        display='flex',
        flex_flow='row',
        align_items='center',
        justify_content='flex-start'
    )
)

# 文件存储区域布局
storage_box = widgets.HBox(
    [storage_path_input, btn_copy],
    layout=widgets.Layout(
        gap='16px',
        margin='10px 0',
        display='flex',
        flex_flow='row',
        align_items='center',
        justify_content='flex-start'
    )
)

# 显示界面
display(widgets.VBox([
    status_label,
    button_box,
    storage_box,
    output
], layout=widgets.Layout(width='100%', margin='0', padding='0')))

# 初始化UI
initialize_ui_state()

# 隐藏所有代码单元格的输入区域
display(Javascript("""
setTimeout(function() {
    document.querySelectorAll('.jp-CodeCell .jp-InputArea').forEach(function(el) {
        el.style.display = 'none';
    });
}, 100);
"""))
