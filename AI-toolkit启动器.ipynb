from IPython.display import HTML, Javascript, display, clear_output
import ipywidgets as widgets
import subprocess
import threading
import time
import os

# 清理之前的输出
clear_output()

# 注入苹果风格 CSS
display(HTML("""
<style>
/* 苹果风格按钮 */
.apple-btn {
    background: linear-gradient(180deg, #0A84FF 0%, #0060DF 100%);
    color: #FFFFFF !important;
    border: none;
    border-radius: 10px;
    padding: 12px 24px;
    font-size: 15px;
    font-weight: 500;
    box-shadow: 0 3px 8px rgba(0,0,0,0.15);
    transition: all 0.2s ease;
    min-width: 160px;
    display: inline-block !important;
    height: auto !important;
    line-height: normal !important;
}
.apple-btn:hover {
    background: linear-gradient(180deg, #379AFF 0%, #007AFF 100%);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    transform: translateY(-1px);
}
.apple-btn:active {
    transform: scale(0.98);
    box-shadow: 0 2px 4px rgba(0,0,0,0.25);
}
.apple-btn:disabled {
    background: #E5E5EA !important;
    color: #8E8E93 !important;
    box-shadow: none !important;
    cursor: not-allowed !important;
    opacity: 0.6 !important;
}

/* JupyterLab 特定的禁用状态样式 */
.widget-button[disabled] {
    background: #E5E5EA !important;
    color: #8E8E93 !important;
    opacity: 0.6 !important;
    pointer-events: none !important;
}

/* 修复 Jupyter 按钮样式 */
.widget-button {
    overflow: visible !important;
}
.jupyter-widgets.widget-button .widget-button {
    height: auto !important;
}

/* 输出区域样式 */
.widget-output {
    background: #F2F2F7;
    border-radius: 8px;
    padding: 12px 16px;
    margin-top: 12px;
}
.widget-output .output_area {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
    font-size: 13px;
    color: #1C1C1E;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
    animation: pulse 2s infinite;
}
.status-success { background: #34C759; }
.status-running { background: #FF9500; }
.status-error { background: #FF3B30; }

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* 修复整体布局 */
.widget-vbox {
    gap: 0 !important;
}
.jp-OutputArea-output {
    overflow: visible !important;
}
</style>
"""))

# 工具函数：检查磁盘空间
def check_disk_space():
    """检查 /root/autodl-tmp/ 目录总空间（GB）"""
    try:
        result = subprocess.run(['df', '/root/autodl-tmp/', '--output=size'], 
                              stdout=subprocess.PIPE, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            if len(lines) >= 2:
                total_kb = int(lines[1])
                total_gb = total_kb / (1024 * 1024)
                return total_gb
    except:
        pass
    return 0

# 工具函数：检查模型是否已存在
def check_models_exist():
    """检查模型文件是否已经存在"""
    # 根据实际脚本中的路径检查
    target_path = '/root/autodl-tmp/black-forest-labs/FLUX.1-Kontext-dev'
    
    if not os.path.exists(target_path):
        return False, None
    
    # 检查必要的模型文件（基于实际脚本内容）
    required_files = {
        'transformer': [
            'diffusion_pytorch_model-00001-of-00003.safetensors',
            'diffusion_pytorch_model-00002-of-00003.safetensors', 
            'diffusion_pytorch_model-00003-of-00003.safetensors'
        ],
        'text_encoder_2': [
            'model-00001-of-00002.safetensors',
            'model-00002-of-00002.safetensors'
        ],
        'main_model': ['flux1-kontext-dev.safetensors']
    }
    
    missing_files = []
    
    # 检查transformer模型文件
    transformer_path = os.path.join(target_path, 'transformer')
    if os.path.exists(transformer_path):
        for file in required_files['transformer']:
            if not os.path.exists(os.path.join(transformer_path, file)):
                missing_files.append(f'transformer/{file}')
    else:
        missing_files.extend([f'transformer/{file}' for file in required_files['transformer']])
    
    # 检查text_encoder_2模型文件
    text_encoder_path = os.path.join(target_path, 'text_encoder_2')
    if os.path.exists(text_encoder_path):
        for file in required_files['text_encoder_2']:
            if not os.path.exists(os.path.join(text_encoder_path, file)):
                missing_files.append(f'text_encoder_2/{file}')
    else:
        missing_files.extend([f'text_encoder_2/{file}' for file in required_files['text_encoder_2']])
    
    # 检查主模型文件
    for file in required_files['main_model']:
        if not os.path.exists(os.path.join(target_path, file)):
            missing_files.append(file)
    
    # 如果没有缺失文件，说明模型完整
    if not missing_files:
        return True, target_path
    
    # 如果只是缺少少数文件，静默处理，不在这里显示
    if len(missing_files) <= 3:
        return False, None
    
    # 如果缺少大量文件，说明模型不完整
    return False, None

# 工具函数：清理缓存
def clean_cache():
    """通用递归清理 autodl-tmp 目录下的缓存、临时文件和回收站"""
    cleaned_size = 0
    
    # 1. 通用递归搜索并清理所有回收站目录
    try:
        trash_search_cmd = r"""find /root/autodl-tmp/ -type d \( \
            -name '.Trash-*' -o -name '.trash' -o -name 'Trash' -o \
            -path '*/.local/share/Trash' \
        \) 2>/dev/null"""
        
        result = subprocess.run(['bash', '-c', trash_search_cmd], 
                              stdout=subprocess.PIPE, text=True)
        if result.stdout.strip():
            for trash_path in result.stdout.strip().split('\n'):
                if trash_path and os.path.exists(trash_path):
                    try:
                        size_result = subprocess.run(['du', '-s', trash_path], 
                                                   stdout=subprocess.PIPE, text=True)
                        if size_result.returncode == 0:
                            size_kb = int(size_result.stdout.split()[0])
                            cleaned_size += size_kb / (1024 * 1024)
                        
                        subprocess.run(['rm', '-rf', trash_path], 
                                     stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                    except:
                        pass
    except:
        pass
    
    # 2. 通用递归搜索并清理所有缓存目录
    try:
        cache_search_cmd = r"""find /root/autodl-tmp/ -type d \( \
            -name '.cache' -o -name 'cache' -o -name 'tmp' -o -name '.tmp' -o \
            -name 'huggingface' -o -name 'hub' -o -name '.huggingface' \
        \) 2>/dev/null"""
        
        result = subprocess.run(['bash', '-c', cache_search_cmd], 
                              stdout=subprocess.PIPE, text=True)
        if result.stdout.strip():
            for cache_path in result.stdout.strip().split('\n'):
                if cache_path and os.path.exists(cache_path):
                    try:
                        size_result = subprocess.run(['du', '-s', cache_path], 
                                                   stdout=subprocess.PIPE, text=True)
                        if size_result.returncode == 0:
                            size_kb = int(size_result.stdout.split()[0])
                            cleaned_size += size_kb / (1024 * 1024)
                        
                        subprocess.run(['rm', '-rf', cache_path], 
                                     stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                    except:
                        pass
    except:
        pass
    
    # 3. 通用递归搜索并清理所有临时文件
    try:
        temp_files_cmd = r"""find /root/autodl-tmp/ \( \
            -name '*.tmp' -o -name '*.part' -o -name '*.download' -o \
            -name '.git*' -o -name '.*~' -o -name '*~' -o \
            -name '.DS_Store' -o -name '*.bak' -o -name '*.old' -o \
            -name '*.swp' -o -name '*.lock' -o -name '*.temp' -o \
            -name '*.log' -o -name '*.pid' -o -name 'core.*' \
        \) -type f 2>/dev/null"""
        
        result = subprocess.run(['bash', '-c', temp_files_cmd], 
                              stdout=subprocess.PIPE, text=True)
        if result.stdout.strip():
            for file_path in result.stdout.strip().split('\n'):
                if file_path and os.path.exists(file_path):
                    try:
                        size = os.path.getsize(file_path)
                        cleaned_size += size / (1024 * 1024 * 1024)
                        os.remove(file_path)
                    except:
                        pass
    except:
        pass
    
    # 4. 智能清理下载临时目录（仅包含非模型文件的目录）
    try:
        temp_dir_patterns = ['*-train', '*-dev', 'zealman', 'download*']
        
        for pattern in temp_dir_patterns:
            find_cmd = f"find /root/autodl-tmp/ -type d -name '{pattern}' 2>/dev/null"
            result = subprocess.run(['bash', '-c', find_cmd], 
                                  stdout=subprocess.PIPE, text=True)
            
            if result.stdout.strip():
                for temp_dir in result.stdout.strip().split('\n'):
                    if temp_dir and os.path.exists(temp_dir):
                        try:
                            # 检查是否只包含非模型文件
                            model_files_cmd = f"find '{temp_dir}' -name '*.safetensors' -o -name '*.bin' -o -name '*.pth' 2>/dev/null"
                            model_result = subprocess.run(['bash', '-c', model_files_cmd], 
                                                        stdout=subprocess.PIPE, text=True)
                            
                            # 如果没有模型文件，可以安全删除
                            if not model_result.stdout.strip():
                                size_result = subprocess.run(['du', '-s', temp_dir], 
                                                           stdout=subprocess.PIPE, text=True)
                                if size_result.returncode == 0:
                                    size_kb = int(size_result.stdout.split()[0])
                                    cleaned_size += size_kb / (1024 * 1024)
                                
                                subprocess.run(['rm', '-rf', temp_dir], 
                                             stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                        except:
                            pass
    except:
        pass

    # 5. 清理空目录（多次清理，因为删除文件后可能产生新的空目录）
    try:
        for _ in range(3):
            empty_dirs_cmd = "find /root/autodl-tmp/ -type d -empty -not -path '/root/autodl-tmp' 2>/dev/null"
            result = subprocess.run(['bash', '-c', empty_dirs_cmd], 
                                  stdout=subprocess.PIPE, text=True)
            if result.stdout.strip():
                for dir_path in result.stdout.strip().split('\n'):
                    if dir_path and os.path.exists(dir_path):
                        try:
                            os.rmdir(dir_path)
                        except:
                            pass
            else:
                break  # 没有更多空目录了
    except:
        pass
    
    return cleaned_size

# 深度清理函数（用于严重空间不足时）
def deep_clean():
    """深度清理 autodl-tmp 目录，包括所有可能的垃圾文件"""
    cleaned_size = 0
    
    try:
        cleaned_size += clean_cache()
        
        # 清理大型日志文件
        log_files_cmd = "find /root/autodl-tmp/ -name '*.log' -size +10M -exec rm -f {} + 2>/dev/null"
        subprocess.run(['bash', '-c', log_files_cmd], 
                      stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        
        # 强制同步
        subprocess.run(['sync'], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        
    except:
        pass
    
    return cleaned_size

# 创建输出区域
output = widgets.Output()
status_label = widgets.HTML(value='')

# 全局变量跟踪服务状态
service_running = False
service_thread = None



# 一键启动函数（合并下载模型和启动UI）
def one_click_start(btn):
    global service_running, service_thread
    
    btn.disabled = True
    btn_clean.disabled = True
    status_label.value = '<span class="status-indicator status-running"></span>正在检查模型...'
    
    with output:
        output.clear_output()
        
        # 步骤1: 检查磁盘空间
        print("💽 正在检查磁盘总空间...")
        status_label.value = '<span class="status-indicator status-running"></span>检查磁盘空间...'
        time.sleep(1)
        
        total_space = check_disk_space()
        
        output.clear_output()
        print(f"💽 当前磁盘总空间: {total_space:.1f}GB")
        
        # 检查空间是否足够（需要至少58GB）
        if total_space < 58:
            print("❌ 磁盘总空间不足！")
            print(f"📊 当前总空间: {total_space:.1f}GB")
            print("📊 最少需要: 58GB")
            print("💡 请扩充数据盘空间后再试")
            status_label.value = '<span class="status-indicator status-error"></span>磁盘空间不足'
            btn.disabled = False
            btn_clean.disabled = False
            return
        
        print("✅ 磁盘空间充足")
        time.sleep(1)
        
        # 步骤2: 检查模型是否已存在
        output.clear_output()
        print(f"💽 当前磁盘总空间: {total_space:.1f}GB ✅")
        print("🔍 正在检查模型文件...")
        status_label.value = '<span class="status-indicator status-running"></span>检查模型文件...'
        time.sleep(1)
        
        models_exist, model_path = check_models_exist()
        
        # 清除上一步信息，显示检查结果
        output.clear_output()
        
        if models_exist:
            print("✅ 模型文件已存在，跳过下载")
            print(f"📁 模型路径: {model_path}")
            status_label.value = '<span class="status-indicator status-running"></span>准备启动UI...'
            
            time.sleep(1)
            
            # 检查服务是否已经运行
            output.clear_output()
            print("✅ 模型文件已存在")
            print("🔍 检查训练UI状态...")
            
            # 清理可能的异常进程
            subprocess.run(['bash', '-c', 'kill -9 $(lsof -ti:6006) 2>/dev/null'], 
                          stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            time.sleep(2)
            
            # 启动服务
            output.clear_output()
            print("✅ 模型文件已存在，跳过下载")
            print("⏳ 正在启动训练UI...")
            status_label.value = '<span class="status-indicator status-running"></span>正在启动UI...'
            
            def run_server():
                global service_running
                try:
                    cmd = '''cd /root/ai-toolkit/ui && 
                             export NVM_DIR="$HOME/.nvm" && 
                             [ -s "$NVM_DIR/nvm.sh" ] && . "$NVM_DIR/nvm.sh" && 
                             npm run start 2>/dev/null'''
                    subprocess.Popen(cmd, shell=True, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                    service_running = True
                except:
                    service_running = False
                    
            service_thread = threading.Thread(target=run_server)
            service_thread.start()
            
            # 等待服务启动
            time.sleep(5)
            
            output.clear_output()
            print("✅ 模型文件已存在，跳过下载")
            print("✅ 训练UI启动成功！")
            print("🌐 请访问: http://localhost:6006")
            
            btn_one_click.disabled = False
            status_label.value = '<span class="status-indicator status-success"></span>启动完成'
            btn_clean.disabled = False
            return

        else:
            print("📥 需要下载模型文件")
            time.sleep(1)
        
        # 空间已在开头检查过，直接开始下载
        time.sleep(1)
        
        # 步骤3: 开始下载
        output.clear_output()
        print("📥 开始下载模型...")
        print("📋 第1步: 复制基础模型文件...")
        status_label.value = '<span class="status-indicator status-running"></span>下载模型中...'
        time.sleep(1)
        
        try:
            result1 = subprocess.run(['/bin/bash', '/root/scripts-aitoolkit/copy_fluxkontext_model.sh'], 
                                    stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True, timeout=300)
            
            # 显示第1步结果
            output.clear_output()
            print("📥 开始下载模型...")
            
            if result1.returncode == 0:
                if result1.stdout and ("已存在" in result1.stdout or "exist" in result1.stdout.lower()):
                    print("✓ 基础模型文件已存在")
                else:
                    print("✓ 基础模型复制完成")
            else:
                print("❌ 基础模型复制失败")
        
        except subprocess.TimeoutExpired:
            output.clear_output()
            print("📥 开始下载模型...")
            print("⏰ 复制超时，清理后重试...")
            clean_cache()
        
        # 3.2: 下载补充组件
        time.sleep(1)
        
        output.clear_output()
        print("📥 开始下载模型...")
        print("✓ 基础模型处理完成")
        print("📋 第2步: 下载补充组件...")
        time.sleep(1)
        
        # 定义需要下载的6个文件
        download_files = [
            ('transformer', 'diffusion_pytorch_model-00001-of-00003.safetensors'),
            ('transformer', 'diffusion_pytorch_model-00002-of-00003.safetensors'),
            ('transformer', 'diffusion_pytorch_model-00003-of-00003.safetensors'),
            ('text_encoder_2', 'model-00001-of-00002.safetensors'),
            ('text_encoder_2', 'model-00002-of-00002.safetensors'),
            ('flux1-kontext-dev', 'flux1-kontext-dev.safetensors')
        ]
        
        total_files = len(download_files)
        completed_files = 0
        
        try:
            # 分别下载每个组件并显示进度
            for i, (component_type, filename) in enumerate(download_files, 1):
                output.clear_output()
                print("📥 开始下载模型...")
                print("✓ 基础模型处理完成")
                print("📋 第2步: 下载补充组件...")
                # 简化文件名显示
                display_name = filename.replace('diffusion_pytorch_model-', 'transformer-').replace('.safetensors', '')
                if 'model-' in filename and 'text_encoder' in component_type:
                    display_name = filename.replace('model-', 'text_encoder-').replace('.safetensors', '')
                elif 'flux1-kontext-dev' in filename:
                    display_name = 'flux1-kontext-dev'
                
                print(f"📁 正在下载 ({i}/{total_files}): {display_name}")
                
                # 检查文件是否已存在
                if component_type == 'flux1-kontext-dev':
                    file_path = f"/root/autodl-tmp/black-forest-labs/FLUX.1-Kontext-dev/{filename}"
                else:
                    file_path = f"/root/autodl-tmp/black-forest-labs/FLUX.1-Kontext-dev/{component_type}/{filename}"
                
                if os.path.exists(file_path):
                    completed_files += 1
                    output.clear_output()
                    print("📥 开始下载模型...")
                    print("✓ 基础模型处理完成")
                    print("📋 第2步: 下载补充组件...")
                    
                    # 计算并显示百分比
                    file_progress = 100
                    overall_progress = (completed_files / total_files) * 100
                    print(f"✓ ({i}/{total_files}) {file_progress}% - {display_name} (已存在)")
                    print(f"📊 总体进度: {overall_progress:.0f}% ({completed_files}/{total_files} 已完成)")
                    
                    # 更新状态
                    status_label.value = f'<span class="status-indicator status-running"></span>下载进度: {overall_progress:.0f}%'
                    time.sleep(0.5)
                    continue
                
                # 执行下载
                status_label.value = f'<span class="status-indicator status-running"></span>下载中: ({i}/{total_files})'
                
                try:
                    if component_type == 'flux1-kontext-dev':
                        result = subprocess.run(['/bin/bash', '/root/scripts-aitoolkit/down_blackfluxkontextmodel.sh', 'flux1-kontext-dev'], 
                                              stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True, timeout=180)
                    else:
                        result = subprocess.run(['/bin/bash', '/root/scripts-aitoolkit/down_blackfluxkontextmodel.sh', component_type], 
                                              stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True, timeout=180)
                    
                    if result.returncode == 0:
                        completed_files += 1
                        output.clear_output()
                        print("📥 开始下载模型...")
                        print("✓ 基础模型处理完成")
                        print("📋 第2步: 下载补充组件...")
                        
                        # 计算并显示百分比
                        file_progress = 100
                        overall_progress = (completed_files / total_files) * 100
                        print(f"✓ ({i}/{total_files}) {file_progress}% - {display_name}")
                        print(f"📊 总体进度: {overall_progress:.0f}% ({completed_files}/{total_files} 已完成)")
                        
                        status_label.value = f'<span class="status-indicator status-running"></span>下载进度: {overall_progress:.0f}%'
                        time.sleep(0.5)
                    else:
                        output.clear_output()
                        print("📥 开始下载模型...")
                        print("✓ 基础模型处理完成")
                        print("📋 第2步: 下载补充组件...")
                        
                        # 显示失败的百分比
                        file_progress = 0
                        print(f"❌ ({i}/{total_files}) {file_progress}% - {display_name}")
                        time.sleep(0.5)
                
                except subprocess.TimeoutExpired:
                    output.clear_output()
                    print("📥 开始下载模型...")
                    print("✓ 基础模型处理完成")
                    print("📋 第2步: 下载补充组件...")
                    
                    # 显示超时的百分比
                    file_progress = 0
                    print(f"⏰ ({i}/{total_files}) {file_progress}% - {display_name} (超时)")
                    time.sleep(0.5)
            
            # 显示最终结果
            output.clear_output()
            print("📥 开始下载模型...")
            print("✓ 基础模型处理完成")
            
            if completed_files == total_files:
                print(f"✅ 补充组件下载完成 ({completed_files}/{total_files})")
            elif completed_files > 0:
                print(f"⚠️ 部分组件下载完成 ({completed_files}/{total_files})")
            else:
                print("❌ 补充组件下载失败")
        
        except subprocess.TimeoutExpired:
            output.clear_output()
            print("⏰ 下载超时，请检查网络")
            status_label.value = '<span class="status-indicator status-error"></span>下载超时'
            btn_one_click.disabled = False
            btn_clean.disabled = False
            return
        except Exception as e:
            output.clear_output()
            print(f"❌ 下载异常: {str(e)}")
            status_label.value = '<span class="status-indicator status-error"></span>下载失败'
            btn_one_click.disabled = False
            btn_clean.disabled = False
            return
        
        # 步骤4: 模型下载完成，开始启动UI
        time.sleep(1)
        
        output.clear_output()
        print("✅ 模型下载完成！")
        
        # 步骤6: 启动训练UI
        # 清理可能的异常进程
        subprocess.run(['bash', '-c', 'kill -9 $(lsof -ti:6006) 2>/dev/null'], 
                      stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        time.sleep(2)
        
        # 启动服务
        output.clear_output()
        print("✅ 模型下载完成！")
        print("⏳ 正在启动训练UI...")
        status_label.value = '<span class="status-indicator status-running"></span>正在启动UI...'
        
        def run_server():
            global service_running
            try:
                cmd = '''cd /root/ai-toolkit/ui && 
                         export NVM_DIR="$HOME/.nvm" && 
                         [ -s "$NVM_DIR/nvm.sh" ] && . "$NVM_DIR/nvm.sh" && 
                         npm run start 2>/dev/null'''
                subprocess.Popen(cmd, shell=True, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                service_running = True
            except:
                service_running = False
                
        service_thread = threading.Thread(target=run_server)
        service_thread.start()
        
        # 等待服务启动
        time.sleep(5)
        
        output.clear_output()
        print("✅ 模型下载完成！")
        print("✅ 训练UI启动成功！")
        print("🌐 请访问: http://localhost:6006")
        

        
        btn_one_click.disabled = False
        status_label.value = '<span class="status-indicator status-success"></span>启动完成'
        
    btn_clean.disabled = False





# 手动清理缓存函数
def manual_clean_cache(btn):
    btn.disabled = True
    with output:
        output.clear_output()
        
        # 步骤1: 检查当前空间
        print("📊 正在检查当前磁盘空间...")
        time.sleep(1)
        
        space_before = check_disk_space()
        
        # 清除上一步，显示空间信息
        output.clear_output()
        print(f"📊 当前磁盘总空间: {space_before:.1f}GB")
        time.sleep(1)
        
        # 步骤2: 开始清理
        output.clear_output()
        print(f"📊 当前磁盘总空间: {space_before:.1f}GB")
        print("🗑️ 正在清理临时文件...")
        time.sleep(1)
        
        cleaned_size = clean_cache()
        space_after = check_disk_space()
        
        # 步骤3: 显示最终结果
        output.clear_output()
        
        if cleaned_size > 0:
            print(f"✅ 清理完成！")
            print(f"📊 释放空间: {cleaned_size:.1f}GB")
            print(f"📊 磁盘总空间: {space_after:.1f}GB")
        else:
            print("ℹ️ 没有发现可清理的文件")
            print(f"📊 磁盘总空间: {space_after:.1f}GB")
        
        print("💡 已清理: 所有回收站、缓存、临时文件、下载临时目录、空目录（智能递归搜索）")
    
    btn.disabled = False

# 创建按钮
btn_one_click = widgets.Button(
    description='一键启动',
    _dom_classes=['apple-btn'],
    layout=widgets.Layout(width='auto', height='40px')
)

btn_clean = widgets.Button(
    description='清理缓存',
    _dom_classes=['apple-btn'],
    layout=widgets.Layout(width='auto', height='40px')
)


# 绑定事件
btn_one_click.on_click(one_click_start)
btn_clean.on_click(manual_clean_cache)

# 页面加载时初始化
def initialize_ui_state():
    """页面加载时初始化UI状态"""
    pass



# 创建布局
button_box = widgets.HBox(
    [btn_one_click, btn_clean],
    layout=widgets.Layout(
        gap='16px', 
        margin='20px 0',
        display='flex',
        flex_flow='row',
        align_items='center',
        justify_content='flex-start'
    )
)

# 显示界面
display(widgets.VBox([
    status_label,
    button_box,
    output
], layout=widgets.Layout(width='100%', margin='0', padding='0')))

# 初始化UI
initialize_ui_state()

# 隐藏所有代码单元格的输入区域
display(Javascript("""
setTimeout(function() {
    document.querySelectorAll('.jp-CodeCell .jp-InputArea').forEach(function(el) {
        el.style.display = 'none';
    });
}, 100);
"""))
