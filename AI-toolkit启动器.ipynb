{"cells": [{"cell_type": "markdown", "metadata": {"vscode": {"languageId": "plaintext"}}, "source": ["介绍\n", "- AI-toolkit 是一款专为  模型训练设计的工具\n", "- 已更新到 **2025年7月8日版本** 对应官方 0.3.4\n", "- 本镜像只保留了**FLUX Kontext Lora**的训练，其他选项已隐藏\n", "\n", "使用说明\n", "1. 点击下方 **「1.下载模型」** 按钮，等待下载完成\n", "2. 点击 **「2.启动训练UI」** 按钮启动服务\n", "3. 访问 [http://localhost:6006](http://localhost:6006) 打开训练界面\n", "4. 如使用 Autodl-ssh 工具连接，端口填写 **6006**\n", "\n", "注意事项\n", "- 🔴 **重要**: 开机前需要确保数据盘总空间不低于 **58G** （训练底模需要较大空间）\n", "- 💬 有问题可以加 Q群：**1046279436**\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["from IPython.display import HTML, Javascript, display, clear_output\n", "import ipywidgets as widgets\n", "import subprocess\n", "import threading\n", "import time\n", "import os\n", "\n", "# 清理之前的输出\n", "clear_output()\n", "\n", "# 注入苹果风格 CSS\n", "display(HTML(\"\"\"\n", "<style>\n", "/* 苹果风格按钮 */\n", ".apple-btn {\n", "    background: linear-gradient(180deg, #0A84FF 0%, #0060DF 100%);\n", "    color: #FFFFFF !important;\n", "    border: none;\n", "    border-radius: 10px;\n", "    padding: 12px 24px;\n", "    font-size: 15px;\n", "    font-weight: 500;\n", "    box-shadow: 0 3px 8px rgba(0,0,0,0.15);\n", "    transition: all 0.2s ease;\n", "    min-width: 160px;\n", "    display: inline-block !important;\n", "    height: auto !important;\n", "    line-height: normal !important;\n", "}\n", ".apple-btn:hover {\n", "    background: linear-gradient(180deg, #379AFF 0%, #007AFF 100%);\n", "    box-shadow: 0 4px 12px rgba(0,0,0,0.2);\n", "    transform: translateY(-1px);\n", "}\n", ".apple-btn:active {\n", "    transform: scale(0.98);\n", "    box-shadow: 0 2px 4px rgba(0,0,0,0.25);\n", "}\n", ".apple-btn:disabled {\n", "    background: #E5E5EA !important;\n", "    color: #8E8E93 !important;\n", "    box-shadow: none !important;\n", "    cursor: not-allowed !important;\n", "    opacity: 0.6 !important;\n", "}\n", "\n", "/* 运行中状态按钮 */\n", ".apple-btn-running {\n", "    background: linear-gradient(180deg, #34C759 0%, #30B350 100%) !important;\n", "    color: #FFFFFF !important;\n", "    border: none;\n", "    border-radius: 10px;\n", "    padding: 12px 24px;\n", "    font-size: 15px;\n", "    font-weight: 500;\n", "    box-shadow: 0 3px 8px rgba(52,199,89,0.3);\n", "    transition: all 0.2s ease;\n", "    min-width: 160px;\n", "    display: inline-block !important;\n", "    height: auto !important;\n", "    line-height: normal !important;\n", "}\n", ".apple-btn-running:hover {\n", "    background: linear-gradient(180deg, #40D368 0%, #34C759 100%) !important;\n", "    box-shadow: 0 4px 12px rgba(52,199,89,0.4);\n", "    transform: translateY(-1px);\n", "}\n", ".apple-btn-running:active {\n", "    transform: scale(0.98);\n", "    box-shadow: 0 2px 4px rgba(52,199,89,0.5);\n", "}\n", "\n", "/* 停止按钮样式 */\n", ".apple-btn-stop {\n", "    background: linear-gradient(180deg, #FF3B30 0%, #D70015 100%) !important;\n", "    color: #FFFFFF !important;\n", "    border: none;\n", "    border-radius: 10px;\n", "    padding: 12px 24px;\n", "    font-size: 15px;\n", "    font-weight: 500;\n", "    box-shadow: 0 3px 8px rgba(255,59,48,0.3);\n", "    transition: all 0.2s ease;\n", "    min-width: 160px;\n", "    display: inline-block !important;\n", "    height: auto !important;\n", "    line-height: normal !important;\n", "}\n", ".apple-btn-stop:hover {\n", "    background: linear-gradient(180deg, #FF453A 0%, #FF3B30 100%) !important;\n", "    box-shadow: 0 4px 12px rgba(255,59,48,0.4);\n", "    transform: translateY(-1px);\n", "}\n", ".apple-btn-stop:active {\n", "    transform: scale(0.98);\n", "    box-shadow: 0 2px 4px rgba(255,59,48,0.5);\n", "}\n", "\n", "/* 紧凑按钮样式 */\n", ".apple-btn-compact {\n", "    background: linear-gradient(180deg, #0A84FF 0%, #0060DF 100%);\n", "    color: #FFFFFF !important;\n", "    border: none;\n", "    border-radius: 6px;\n", "    padding: 6px 12px;\n", "    font-size: 13px;\n", "    font-weight: 500;\n", "    box-shadow: 0 2px 6px rgba(0,0,0,0.12);\n", "    transition: all 0.2s ease;\n", "    display: inline-block !important;\n", "    height: auto !important;\n", "    line-height: normal !important;\n", "}\n", ".apple-btn-compact:hover {\n", "    background: linear-gradient(180deg, #379AFF 0%, #007AFF 100%);\n", "    box-shadow: 0 3px 8px rgba(0,0,0,0.16);\n", "    transform: translateY(-1px);\n", "}\n", ".apple-btn-compact:active {\n", "    transform: scale(0.98);\n", "    box-shadow: 0 1px 3px rgba(0,0,0,0.2);\n", "}\n", ".apple-btn-compact:disabled {\n", "    background: #E5E5EA !important;\n", "    color: #8E8E93 !important;\n", "    box-shadow: none !important;\n", "    cursor: not-allowed !important;\n", "    opacity: 0.6 !important;\n", "}\n", "\n", "/* JupyterLab 特定的禁用状态样式 */\n", ".widget-button[disabled] {\n", "    background: #E5E5EA !important;\n", "    color: #8E8E93 !important;\n", "    opacity: 0.6 !important;\n", "    pointer-events: none !important;\n", "}\n", "\n", "/* 修复 Jupyter 按钮样式 */\n", ".widget-button {\n", "    overflow: visible !important;\n", "}\n", ".jupyter-widgets.widget-button .widget-button {\n", "    height: auto !important;\n", "}\n", "\n", "/* 输出区域样式 */\n", ".widget-output {\n", "    background: #F2F2F7;\n", "    border-radius: 8px;\n", "    padding: 12px 16px;\n", "    margin-top: 12px;\n", "}\n", ".widget-output .output_area {\n", "    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;\n", "    font-size: 13px;\n", "    color: #1C1C1E;\n", "}\n", "\n", "/* 状态指示器 */\n", ".status-indicator {\n", "    display: inline-block;\n", "    width: 8px;\n", "    height: 8px;\n", "    border-radius: 50%;\n", "    margin-right: 8px;\n", "    animation: pulse 2s infinite;\n", "}\n", ".status-success { background: #34C759; }\n", ".status-running { background: #FF9500; }\n", ".status-error { background: #FF3B30; }\n", "\n", "@keyframes pulse {\n", "    0% { opacity: 1; }\n", "    50% { opacity: 0.5; }\n", "    100% { opacity: 1; }\n", "}\n", "\n", "/* 修复整体布局 */\n", ".widget-vbox {\n", "    gap: 0 !important;\n", "}\n", ".jp-OutputArea-output {\n", "    overflow: visible !important;\n", "}\n", "</style>\n", "\"\"\"))\n", "\n", "# 工具函数：检查模型下载脚本状态\n", "def check_download_script():\n", "    \"\"\"检查下载脚本是否存在\"\"\"\n", "    script_path = '/root/scripts-aitoolkit/down_blackfluxkontextmodel.sh'\n", "    return os.path.exists(script_path)\n", "\n", "# 工具函数：运行模型下载脚本\n", "def run_download_script():\n", "    \"\"\"运行模型下载脚本，返回(成功状态, 输出信息)\"\"\"\n", "    try:\n", "        result = subprocess.run(\n", "            ['/bin/bash', '/root/scripts-aitoolkit/down_blackfluxkontextmodel.sh', 'all'],\n", "            stdout=subprocess.PIPE, \n", "            stderr=subprocess.STDOUT, \n", "            text=True, \n", "            timeout=1800  # 30分钟超时\n", "        )\n", "        return result.returncode == 0, result.stdout\n", "    except subprocess.TimeoutExpired:\n", "        return False, \"下载超时（30分钟）\"\n", "    except Exception as e:\n", "        return False, f\"执行异常: {str(e)}\"\n", "\n", "# 工具函数：运行模型下载脚本并实时显示输出\n", "def run_download_script_with_progress():\n", "    \"\"\"运行模型下载脚本并实时显示所有输出\"\"\"\n", "    try:\n", "        # 启动子进程\n", "        process = subprocess.Popen(\n", "            ['/bin/bash', '/root/scripts-aitoolkit/down_blackfluxkontextmodel.sh', 'all'],\n", "            stdout=subprocess.PIPE,\n", "            stderr=subprocess.STDOUT,\n", "            text=True,\n", "            bufsize=1,\n", "            universal_newlines=True\n", "        )\n", "        \n", "        output_lines = []\n", "        \n", "        # 实时读取并显示输出\n", "        while True:\n", "            line = process.stdout.readline()\n", "            if not line and process.poll() is not None:\n", "                break\n", "            \n", "            if line:\n", "                line_content = line.rstrip()\n", "                output_lines.append(line_content)\n", "                \n", "                # 直接打印每一行，不清空输出\n", "                print(line_content)\n", "        \n", "        # 等待进程完成\n", "        return_code = process.wait()\n", "        \n", "        return return_code == 0, '\\n'.join(output_lines)\n", "        \n", "    except Exception as e:\n", "        return False, f\"执行异常: {str(e)}\"\n", "\n", "# 工具函数：检测端口状态\n", "def check_port_status(port=6006):\n", "    \"\"\"检测指定端口是否有服务在运行\"\"\"\n", "    try:\n", "        result = subprocess.run(['bash', '-c', f'lsof -ti:{port}'], \n", "                              stdout=subprocess.PIPE, stderr=subprocess.DEVNULL, text=True)\n", "        return result.returncode == 0 and result.stdout.strip() != ''\n", "    except:\n", "        return False\n", "\n", "# 工具函数：启动训练UI\n", "def start_training_ui():\n", "    \"\"\"启动训练UI服务\"\"\"\n", "    try:\n", "        # 清理可能的异常进程\n", "        subprocess.run(['bash', '-c', 'kill -9 $(lsof -ti:6006) 2>/dev/null'], \n", "                      stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)\n", "        time.sleep(2)\n", "        \n", "        # 启动服务\n", "        cmd = '''cd /root/ai-toolkit/ui && \n", "                 export NVM_DIR=\"$HOME/.nvm\" && \n", "                 [ -s \"$NVM_DIR/nvm.sh\" ] && . \"$NVM_DIR/nvm.sh\" && \n", "                 npm run start 2>/dev/null'''\n", "        subprocess.Popen(cmd, shell=True, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)\n", "        return True\n", "    except:\n", "        return False\n", "\n", "# 工具函数：停止训练UI服务\n", "def stop_training_ui():\n", "    \"\"\"停止训练UI服务\"\"\"\n", "    try:\n", "        result = subprocess.run(['bash', '-c', 'kill -9 $(lsof -ti:6006) 2>/dev/null'], \n", "                              stdout=subprocess.PIPE, stderr=subprocess.DEVNULL, text=True)\n", "        time.sleep(2)\n", "        return not check_port_status(6006)  # 返回是否成功停止\n", "    except:\n", "        return False\n", "\n", "# 工具函数：设置按钮为运行状态\n", "def set_button_running_state():\n", "    \"\"\"设置按钮为运行中状态\"\"\"\n", "    btn_one_click.description = '运行中'\n", "    btn_one_click._dom_classes = ['apple-btn-running']\n", "    btn_one_click.disabled = False\n", "    status_label.value = '<span class=\"status-indicator status-success\"></span>训练UI运行中'\n", "    btn_stop.style.display = 'block'  # 显示停止按钮\n", "\n", "# 工具函数：设置按钮为正常状态\n", "def set_button_normal_state():\n", "    \"\"\"设置按钮为正常状态\"\"\"\n", "    btn_one_click.description = '一键启动'\n", "    btn_one_click._dom_classes = ['apple-btn']\n", "    btn_one_click.disabled = False\n", "    status_label.value = ''\n", "    btn_stop.style.display = 'none'  # 隐藏停止按钮\n", "\n", "# 工具函数：检查文件存储目录\n", "def check_file_storage():\n", "    \"\"\"检查文件存储目录是否可用\"\"\"\n", "    storage_path = '/root/autodl-fs'\n", "    try:\n", "        # 检查是否是真实的目录（不是文件）\n", "        if os.path.isdir(storage_path):\n", "            # 尝试在目录中创建测试文件来验证可写性\n", "            test_file = os.path.join(storage_path, '.test_write')\n", "            try:\n", "                with open(test_file, 'w') as f:\n", "                    f.write('test')\n", "                os.remove(test_file)\n", "                return True, '文件存储目录可用'\n", "            except:\n", "                return False, '文件存储目录存在但不可写'\n", "        else:\n", "            return False, '文件存储目录不存在或不是目录'\n", "    except:\n", "        return False, '无法访问文件存储目录'\n", "\n", "# 工具函数：复制训练结果\n", "def copy_training_results(target_path):\n", "    \"\"\"复制训练结果到指定路径\"\"\"\n", "    try:\n", "        # 确保目标目录存在\n", "        os.makedirs(target_path, exist_ok=True)\n", "        \n", "        # 执行复制脚本\n", "        copy_script = f'''#!/bin/bash\n", "copied_count=0\n", "for dir in /root/output/*; do\n", "    if [ -d \"$dir\" ]; then\n", "        base=$(basename \"$dir\")\n", "        target=\"{target_path}/$base\"\n", "        if [ -e \"$target\" ]; then\n", "            timestamp=$(date +%Y%m%d%H%M%S)\n", "            target=\"${{target}}_$timestamp\"\n", "        fi\n", "        echo \"正在复制: $base -> $(basename \"$target\")\"\n", "        rsync -a --info=progress2 \"$dir\" \"$target\"\n", "        if [ $? -eq 0 ]; then\n", "            echo \"复制完成: $(basename \"$target\")\"\n", "            copied_count=$((copied_count + 1))\n", "        else\n", "            echo \"复制失败: $(basename \"$target\")\"\n", "        fi\n", "    fi\n", "done\n", "echo \"COPIED_COUNT:$copied_count\"\n", "'''\n", "        \n", "        result = subprocess.run(\n", "            ['bash', '-c', copy_script],\n", "            stdout=subprocess.PIPE,\n", "            stderr=subprocess.STDOUT,\n", "            text=True,\n", "            timeout=600  # 10分钟超时\n", "        )\n", "        \n", "        # 检查实际复制的文件数量\n", "        copied_count = 0\n", "        if result.stdout:\n", "            for line in result.stdout.split('\\n'):\n", "                if line.startswith('COPIED_COUNT:'):\n", "                    try:\n", "                        copied_count = int(line.split(':')[1])\n", "                    except:\n", "                        pass\n", "                    break\n", "        \n", "        # 如果没有复制任何文件，返回失败\n", "        if copied_count == 0:\n", "            return False, \"没有找到可复制的训练结果目录\"\n", "        \n", "        return result.returncode == 0, result.stdout\n", "    except subprocess.TimeoutExpired:\n", "        return False, \"复制超时（10分钟）\"\n", "    except Exception as e:\n", "        return False, f\"复制异常: {str(e)}\"\n", "\n", "# 创建输出区域\n", "output = widgets.Output()\n", "status_label = widgets.HTML(value='')\n", "\n", "# 全局变量跟踪服务状态\n", "service_running = False\n", "service_thread = None\n", "\n", "\n", "\n", "# 一键启动函数（简化版）\n", "def one_click_start(btn):\n", "    global service_running, service_thread\n", "    \n", "    # 如果按钮是运行中状态，检查服务状态\n", "    if btn.description == '运行中':\n", "        with output:\n", "            output.clear_output()\n", "            print(\"🔍 正在检查训练UI状态...\")\n", "            \n", "            if check_port_status(6006):\n", "                print(\"✅ 训练UI正在运行中\")\n", "                print(\"🌐 请访问: http://localhost:6006\")\n", "                print(\"💡 如需重启服务，请先停止当前服务\")\n", "            else:\n", "                print(\"⚠️ 检测到服务已停止，恢复启动按钮\")\n", "                set_button_normal_state()\n", "        return\n", "    \n", "    btn.disabled = True\n", "    btn_clean.disabled = True\n", "    status_label.value = '<span class=\"status-indicator status-running\"></span>正在准备模型...'\n", "    \n", "    with output:\n", "        output.clear_output()\n", "        \n", "        # 步骤0: 检查端口状态\n", "        if check_port_status(6006):\n", "            print(\"✅ 检测到训练UI已在运行\")\n", "            print(\"🌐 请访问: http://localhost:6006\")\n", "            set_button_running_state()\n", "            btn_clean.disabled = False\n", "            return\n", "        \n", "        # 步骤1: 检查下载脚本\n", "        print(\"🔍 正在检查系统环境...\")\n", "        if not check_download_script():\n", "            print(\"❌ 下载脚本不存在！\")\n", "            print(\"📁 请确保 /root/scripts-aitoolkit/down_blackfluxkontextmodel.sh 文件存在\")\n", "            status_label.value = '<span class=\"status-indicator status-error\"></span>系统环境异常'\n", "            btn.disabled = False\n", "            btn_clean.disabled = False\n", "            return\n", "        \n", "        print(\"✅ 系统环境正常\")\n", "        time.sleep(1)\n", "        \n", "        # 步骤2: 运行模型下载脚本（包含空间检查、清理、下载、验证）\n", "        output.clear_output()\n", "        print(\"📥 正在运行模型管理脚本...\")\n", "        print(\"💡 脚本将自动执行：磁盘检查 → 垃圾清理 → 模型下载 → 完整性验证\")\n", "        status_label.value = '<span class=\"status-indicator status-running\"></span>运行模型管理脚本...'\n", "        time.sleep(2)\n", "        \n", "        # 显示初始信息\n", "        output.clear_output()\n", "        print(\"📥 模型管理脚本运行中...\")\n", "        print(\"⏳ 请耐心等待，这一般来说1-2分钟内就OK\")\n", "        print(\"📋 正在执行：磁盘空间检查、垃圾文件清理、模型下载和验证\")\n", "        print(\"\")\n", "        \n", "        # 运行下载脚本并实时显示所有输出\n", "        success, script_output = run_download_script_with_progress()\n", "        \n", "        # 显示脚本执行结果\n", "        output.clear_output()\n", "        \n", "        if success:\n", "            print(\"✅ 模型管理脚本执行成功！\")\n", "            print(\"📋 所有模型文件已准备就绪\")\n", "            \n", "            # 显示部分脚本输出（最后几行）\n", "            if script_output:\n", "                lines = script_output.strip().split('\\n')\n", "                if len(lines) > 3:\n", "                    print(\"📄 脚本执行摘要:\")\n", "                    for line in lines[-3:]:\n", "                        if line.strip():\n", "                            print(f\"   {line}\")\n", "            \n", "            time.sleep(2)\n", "            \n", "            # 启动训练UI\n", "            output.clear_output()\n", "            print(\"✅ 模型文件准备完成\")\n", "            print(\"⏳ 正在启动训练UI...\")\n", "            status_label.value = '<span class=\"status-indicator status-running\"></span>正在启动UI...'\n", "            \n", "            def run_server():\n", "                global service_running\n", "                service_running = start_training_ui()\n", "                    \n", "            service_thread = threading.Thread(target=run_server)\n", "            service_thread.start()\n", "            \n", "            # 等待服务启动\n", "            time.sleep(5)\n", "            \n", "            output.clear_output()\n", "            print(\"✅ 模型文件准备完成\")\n", "            print(\"✅ 训练UI启动成功！\")\n", "            print(\"🌐 请访问: http://localhost:6006\")\n", "            \n", "            # 设置按钮为运行中状态\n", "            set_button_running_state()\n", "            btn_clean.disabled = False\n", "            return\n", "        \n", "        else:\n", "            print(\"❌ 模型管理脚本执行失败！\")\n", "            \n", "            # 显示错误信息\n", "            if script_output:\n", "                lines = script_output.strip().split('\\n')\n", "                print(\"📄 错误详情:\")\n", "                for line in lines[-5:]:  # 显示最后5行\n", "                    if line.strip():\n", "                        print(f\"   {line}\")\n", "            \n", "            status_label.value = '<span class=\"status-indicator status-error\"></span>模型准备失败'\n", "            btn.disabled = False\n", "            btn_clean.disabled = False\n", "            return\n", "\n", "    btn_clean.disabled = False\n", "\n", "# 停止服务函数\n", "def stop_service(btn):\n", "    global service_running, service_thread\n", "    \n", "    btn.disabled = True\n", "    \n", "    with output:\n", "        output.clear_output()\n", "        print(\"🛑 正在停止训练UI服务...\")\n", "        \n", "        if stop_training_ui():\n", "            print(\"✅ 训练UI服务已停止\")\n", "            set_button_normal_state()\n", "            service_running = False\n", "        else:\n", "            print(\"⚠️ 停止服务时出现问题，请手动检查\")\n", "            # 仍然重置按钮状态，因为可能服务已经停止\n", "            if not check_port_status(6006):\n", "                set_button_normal_state()\n", "                service_running = False\n", "    \n", "    btn.disabled = False\n", "\n", "\n", "# 复制训练结果函数\n", "def copy_results(btn):\n", "    btn.disabled = True\n", "    btn_one_click.disabled = True\n", "    \n", "    with output:\n", "        output.clear_output()\n", "        \n", "        # 获取目标路径\n", "        target_path = storage_path_input.value.strip()\n", "        if not target_path:\n", "            print(\"❌ 请输入文件存储路径\")\n", "            btn.disabled = False\n", "            btn_one_click.disabled = False\n", "            return\n", "        \n", "        # 检查文件存储目录\n", "        print(\"🔍 正在检查文件存储目录...\")\n", "        storage_available, storage_msg = check_file_storage()\n", "        \n", "        if not storage_available:\n", "            print(f\"❌ {storage_msg}\")\n", "            print(\"\")\n", "            print(\"💡 解决方法:\")\n", "            print(\"   1. 到控制台左侧的'文件存储'开通机器所在区域的文件存储功能\")\n", "            print(\"   2. 初始化文件存储\")\n", "            print(\"   3. 重新启动机器\")\n", "            print(\"   4. 确保 /root/autodl-fs 是一个可访问的目录\")\n", "            btn.disabled = False\n", "            btn_one_click.disabled = False\n", "            return\n", "        \n", "        print(f\"✅ {storage_msg}\")\n", "        time.sleep(1)\n", "        \n", "        # 检查训练结果目录\n", "        output.clear_output()\n", "        print(f\"✅ 文件存储目录可用\")\n", "        print(\"🔍 正在检查训练结果...\")\n", "        \n", "        if not os.path.exists('/root/output'):\n", "            print(\"❌ 训练结果目录不存在: /root/output\")\n", "            print(\"💡 请先完成模型训练后再复制结果\")\n", "            btn.disabled = False\n", "            btn_one_click.disabled = False\n", "            return\n", "        \n", "        # 检查是否有训练结果\n", "        try:\n", "            result_dirs = [d for d in os.listdir('/root/output') \n", "                          if os.path.isdir(os.path.join('/root/output', d))]\n", "            if not result_dirs:\n", "                print(\"❌ 你还没有训练结果，快去炼丹吧\")\n", "                print(\"💡 请先完成模型训练后再复制结果\")\n", "                btn.disabled = False\n", "                btn_one_click.disabled = False\n", "                return\n", "        except:\n", "            print(\"❌ 无法读取训练结果目录\")\n", "            btn.disabled = False\n", "            btn_one_click.disabled = False\n", "            return\n", "        \n", "        print(f\"✅ 发现 {len(result_dirs)} 个训练结果目录\")\n", "        time.sleep(1)\n", "        \n", "        # 开始复制\n", "        output.clear_output()\n", "        print(f\"✅ 文件存储目录可用\")\n", "        print(f\"✅ 发现 {len(result_dirs)} 个训练结果目录\")\n", "        print(\"📁 正在复制训练结果...\")\n", "        print(f\"📍 目标路径: {target_path}\")\n", "        print(\"\")\n", "        \n", "        # 执行复制\n", "        success, copy_output = copy_training_results(target_path)\n", "        \n", "        output.clear_output()\n", "        \n", "        if success:\n", "            print(\"✅ 训练结果复制完成！\")\n", "            print(f\"📍 目标路径: {target_path}\")\n", "            \n", "            # 显示复制详情\n", "            if copy_output:\n", "                lines = copy_output.strip().split('\\n')\n", "                print(\"\")\n", "                print(\"📄 复制详情:\")\n", "                for line in lines:\n", "                    if line.strip() and ('正在复制:' in line or '复制完成:' in line):\n", "                        print(f\"   {line}\")\n", "        else:\n", "            print(\"❌ 训练结果复制失败！\")\n", "            if copy_output:\n", "                lines = copy_output.strip().split('\\n')\n", "                print(\"\")\n", "                print(\"📄 错误详情:\")\n", "                for line in lines[-5:]:  # 显示最后5行\n", "                    if line.strip():\n", "                        print(f\"   {line}\")\n", "    \n", "    btn.disabled = False\n", "    btn_one_click.disabled = False\n", "\n", "# 手动清理缓存函数（简化版）\n", "def manual_clean_cache(btn):\n", "    btn.disabled = True\n", "    with output:\n", "        output.clear_output()\n", "        \n", "        print(\"🗑️ 正在运行垃圾文件清理...\")\n", "        print(\"💡 将清理：.downloading文件、临时文件、缓存目录、回收站等\")\n", "        time.sleep(1)\n", "        \n", "        try:\n", "            # 运行清理脚本（只执行清理部分）\n", "            result = subprocess.run(\n", "                ['/bin/bash', '-c', \n", "                 'source /root/scripts-aitoolkit/down_blackfluxkontextmodel.sh && clean_cache'],\n", "                stdout=subprocess.PIPE, \n", "                stderr=subprocess.STDOUT, \n", "                text=True, \n", "                timeout=300\n", "            )\n", "            \n", "            output.clear_output()\n", "            \n", "            if result.returncode == 0:\n", "                print(\"✅ 垃圾文件清理完成！\")\n", "                \n", "                # 显示清理结果\n", "                if result.stdout:\n", "                    lines = result.stdout.strip().split('\\n')\n", "                    for line in lines[-3:]:  # 显示最后3行\n", "                        if line.strip():\n", "                            print(f\"   {line}\")\n", "            else:\n", "                print(\"⚠️ 清理过程中出现一些问题，但已尽力清理\")\n", "                \n", "        except subprocess.TimeoutExpired:\n", "            print(\"⏰ 清理超时，但部分文件可能已被清理\")\n", "        except Exception as e:\n", "            print(f\"❌ 清理异常: {str(e)}\")\n", "        \n", "        print(\"💡 清理范围: /root/autodl-tmp/ 下的所有垃圾文件\")\n", "    \n", "    btn.disabled = False\n", "\n", "# 创建按钮\n", "btn_one_click = widgets.Button(\n", "    description='一键启动',\n", "    _dom_classes=['apple-btn'],\n", "    layout=widgets.Layout(width='auto', height='40px')\n", ")\n", "\n", "btn_stop = widgets.Button(\n", "    description='停止服务',\n", "    _dom_classes=['apple-btn-stop'],\n", "    layout=widgets.Layout(width='auto', height='40px', display='none')\n", ")\n", "\n", "btn_clean = widgets.Button(\n", "    description='清理缓存',\n", "    _dom_classes=['apple-btn'],\n", "    layout=widgets.Layout(width='auto', height='40px')\n", ")\n", "\n", "# 文件存储路径输入框\n", "storage_path_input = widgets.Text(\n", "    value='/root/autodl-fs/models/loras/kontext_lora/',\n", "    placeholder='请输入文件存储路径',\n", "    description='',\n", "    layout=widgets.Layout(width='364px', height='28px'),\n", "    style={'description_width': '0px'}\n", ")\n", "\n", "# 复制训练结果按钮\n", "btn_copy = widgets.Button(\n", "    description='备份结果到这',\n", "    _dom_classes=['apple-btn-compact'],\n", "    layout=widgets.Layout(width='120px', height='28px')\n", ")\n", "\n", "\n", "# 绑定事件\n", "btn_one_click.on_click(one_click_start)\n", "btn_stop.on_click(stop_service)\n", "btn_clean.on_click(manual_clean_cache)\n", "btn_copy.on_click(copy_results)\n", "\n", "# 页面加载时初始化\n", "def initialize_ui_state():\n", "    \"\"\"页面加载时初始化UI状态\"\"\"\n", "    # 检查端口状态，如果服务正在运行则设置按钮为运行状态\n", "    if check_port_status(6006):\n", "        set_button_running_state()\n", "        with output:\n", "            output.clear_output()\n", "            print(\"✅ 检测到训练UI正在运行\")\n", "            print(\"🌐 请访问: http://localhost:6006\")\n", "            print(\"💡 点击'运行中'按钮可查看服务状态\")\n", "    else:\n", "        set_button_normal_state()\n", "\n", "\n", "\n", "# 创建布局\n", "button_box = widgets.HBox(\n", "    [btn_one_click, btn_stop, btn_clean],\n", "    layout=widgets.Layout(\n", "        gap='16px', \n", "        margin='20px 0',\n", "        display='flex',\n", "        flex_flow='row',\n", "        align_items='center',\n", "        justify_content='flex-start'\n", "    )\n", ")\n", "\n", "# 文件存储区域布局\n", "storage_box = widgets.HBox(\n", "    [storage_path_input, btn_copy],\n", "    layout=widgets.Layout(\n", "        gap='16px',\n", "        margin='10px 0',\n", "        display='flex',\n", "        flex_flow='row',\n", "        align_items='center',\n", "        justify_content='flex-start'\n", "    )\n", ")\n", "\n", "# 显示界面\n", "display(widgets.VBox([\n", "    status_label,\n", "    button_box,\n", "    storage_box,\n", "    output\n", "], layout=widgets.Layout(width='100%', margin='0', padding='0')))\n", "\n", "# 初始化UI\n", "initialize_ui_state()\n", "\n", "# 隐藏所有代码单元格的输入区域\n", "display(Javascript(\"\"\"\n", "setTimeout(function() {\n", "    document.querySelectorAll('.jp-CodeCell .jp-InputArea').forEach(function(el) {\n", "        el.style.display = 'none';\n", "    });\n", "}, 100);\n", "\"\"\"))\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 4}