#!/bin/bash

# FLUX.1-Kontext-dev模型文件拷贝脚本
# 将/root/scripts-aitoolkit/FLUX.1-Kontext-dev内的文件拷贝到/root/autodl-tmp/black-forest-labs/FLUX.1-Kontext-dev

echo "开始拷贝FLUX.1-Kontext-dev模型文件..."

# 源目录和目标目录
SOURCE_DIR="/root/scripts-aitoolkit/FLUX.1-Kontext-dev"
TARGET_DIR="/root/autodl-tmp/black-forest-labs/FLUX.1-Kontext-dev"

# 检查源目录是否存在
if [ ! -d "$SOURCE_DIR" ]; then
    echo "错误：源目录 $SOURCE_DIR 不存在！"
    exit 1
fi

# 创建目标目录（如果不存在）
mkdir -p "$TARGET_DIR"

# 拷贝所有文件和目录（仅当目标不存在或大小不一致时覆盖）
echo "正在拷贝文件从 $SOURCE_DIR 到 $TARGET_DIR..."
# 如果系统安装了 rsync，优先使用 rsync 以提高效率
if command -v rsync >/dev/null 2>&1; then
    rsync -av --size-only "$SOURCE_DIR"/ "$TARGET_DIR"/
else
    # 手动遍历源目录中的文件，按需拷贝
    find "$SOURCE_DIR" -type f | while read -r src_file; do
        rel_path="${src_file#$SOURCE_DIR/}"
        dest_file="$TARGET_DIR/$rel_path"
        dest_dir="$(dirname "$dest_file")"
        mkdir -p "$dest_dir"
        
        if [ -f "$dest_file" ]; then
            src_size=$(stat -c%s "$src_file")
            dest_size=$(stat -c%s "$dest_file")
            if [ "$src_size" -eq "$dest_size" ]; then
                echo "跳过已存在且大小一致的文件: $rel_path"
                continue
            else
                echo "覆盖大小不一致的文件: $rel_path"
            fi
        fi
        
        cp "$src_file" "$dest_file"
    done
fi

# 检查拷贝是否成功
if [ $? -eq 0 ]; then
    echo "✅ FLUX.1-Kontext-dev模型文件拷贝完成！"
    echo "目标目录: $TARGET_DIR"
    
    # 显示拷贝的文件列表
    echo "已拷贝的文件和目录："
    ls -la "$TARGET_DIR"
else
    echo "❌ 拷贝过程中出现错误！"
    exit 1
fi

echo "模型文件准备完成，可以开始使用了！" 