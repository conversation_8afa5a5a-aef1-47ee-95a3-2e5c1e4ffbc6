#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "数据盘垃圾文件清理脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -v, --verbose  详细模式，显示清理的文件"
    echo "  -d, --dry-run  预览模式，只显示将要清理的文件，不实际删除"
    echo "  -s, --silent   静默模式，不显示详细信息"
    echo ""
    echo "清理内容:"
    echo "  • 临时文件 (.tmp, .part, .download, .downloading等)"
    echo "  • 备份文件 (.bak, .old, *~等)"
    echo "  • 系统文件 (.DS_Store, .git*, *.swp等)"
    echo "  • 日志文件 (*.log, *.pid等)"
    echo "  • 缓存目录 (.cache, huggingface, .trash等)"
    echo "  • 空目录"
    echo ""
    echo "示例:"
    echo "  $0              # 正常清理"
    echo "  $0 -v           # 详细模式清理"
    echo "  $0 -d           # 预览将要清理的文件"
    echo "  $0 -s           # 静默清理"
}

# 清理垃圾文件
clean_cache() {
    local verbose=$1
    local dry_run=$2
    local silent=$3
    
    if [ "$silent" != "true" ]; then
        log_info "开始清理数据盘垃圾文件..."
    fi

    local cleaned_size=0
    local cleaned_files=0
    local cleaned_dirs=0
    local temp_file="/tmp/cleanup_size_$$"

    # 清理临时文件和垃圾文件
    if [ "$silent" != "true" ] && [ "$verbose" = "true" ]; then
        log_info "正在扫描临时文件..."
    fi

    find /root/autodl-tmp/ \( \
        -name '*.tmp' -o -name '*.part' -o -name '*.download' -o -name '*.downloading' -o \
        -name '.git*' -o -name '.*~' -o -name '*~' -o \
        -name '.DS_Store' -o -name '*.bak' -o -name '*.old' -o \
        -name '*.swp' -o -name '*.lock' -o -name '*.temp' -o \
        -name '*.log' -o -name '*.pid' -o -name 'core.*' -o \
        -name '*.incomplete' -o -name '*.partial' \
    \) -type f 2>/dev/null | while read -r file; do
        if [ -f "$file" ]; then
            local size=$(stat -c%s "$file" 2>/dev/null || echo 0)
            local size_mb=$((size / 1024 / 1024))
            
            if [ "$verbose" = "true" ] && [ "$silent" != "true" ]; then
                echo "  发现文件: $file (${size_mb}MB)"
            fi
            
            if [ "$dry_run" != "true" ]; then
                echo $size_mb >> "$temp_file"
                rm -f "$file" 2>/dev/null
                echo "1" >> "${temp_file}_files"
            else
                echo $size_mb >> "$temp_file"
                echo "1" >> "${temp_file}_files"
            fi
        fi
    done

    # 清理缓存目录
    if [ "$silent" != "true" ] && [ "$verbose" = "true" ]; then
        log_info "正在扫描缓存目录..."
    fi

    find /root/autodl-tmp/ -type d \( \
        -name '.cache' -o -name 'cache' -o -name 'tmp' -o -name '.tmp' -o \
        -name 'huggingface' -o -name 'hub' -o -name '.huggingface' -o \
        -name '.Trash-*' -o -name '.trash' -o -name 'Trash' \
    \) 2>/dev/null | while read -r dir; do
        if [ -d "$dir" ]; then
            local size=$(du -sm "$dir" 2>/dev/null | cut -f1 || echo 0)
            
            if [ "$verbose" = "true" ] && [ "$silent" != "true" ]; then
                echo "  发现目录: $dir (${size}MB)"
            fi
            
            if [ "$dry_run" != "true" ]; then
                echo $size >> "$temp_file"
                rm -rf "$dir" 2>/dev/null
                echo "1" >> "${temp_file}_dirs"
            else
                echo $size >> "$temp_file"
                echo "1" >> "${temp_file}_dirs"
            fi
        fi
    done

    # 清理空目录
    if [ "$silent" != "true" ] && [ "$verbose" = "true" ]; then
        log_info "正在清理空目录..."
    fi

    for i in {1..3}; do
        find /root/autodl-tmp/ -type d -empty -not -path '/root/autodl-tmp' 2>/dev/null | while read -r dir; do
            if [ "$verbose" = "true" ] && [ "$silent" != "true" ]; then
                echo "  清理空目录: $dir"
            fi
            
            if [ "$dry_run" != "true" ]; then
                rmdir "$dir" 2>/dev/null
            fi
        done
    done

    # 计算清理的总大小和数量
    if [ -f "$temp_file" ]; then
        cleaned_size=$(awk '{sum += $1} END {print sum+0}' "$temp_file")
        rm -f "$temp_file"
    fi

    if [ -f "${temp_file}_files" ]; then
        cleaned_files=$(wc -l < "${temp_file}_files")
        rm -f "${temp_file}_files"
    fi

    if [ -f "${temp_file}_dirs" ]; then
        cleaned_dirs=$(wc -l < "${temp_file}_dirs")
        rm -f "${temp_file}_dirs"
    fi

    # 显示结果
    if [ "$silent" != "true" ]; then
        if [ "$dry_run" = "true" ]; then
            log_info "=== 预览结果 ==="
            log_info "将要清理的文件: ${cleaned_files} 个"
            log_info "将要清理的目录: ${cleaned_dirs} 个"
            if [ $cleaned_size -gt 0 ]; then
                log_info "将要释放的空间: ${cleaned_size}MB"
            fi
            log_warning "这是预览模式，没有实际删除任何文件"
        else
            if [ $cleaned_size -gt 0 ] || [ $cleaned_files -gt 0 ] || [ $cleaned_dirs -gt 0 ]; then
                log_success "垃圾文件清理完成！"
                if [ $cleaned_files -gt 0 ]; then
                    log_success "清理文件: ${cleaned_files} 个"
                fi
                if [ $cleaned_dirs -gt 0 ]; then
                    log_success "清理目录: ${cleaned_dirs} 个"
                fi
                if [ $cleaned_size -gt 0 ]; then
                    log_success "释放空间: ${cleaned_size}MB"
                fi
            else
                log_success "没有发现需要清理的垃圾文件"
            fi
        fi
    fi

    return 0
}

# 主函数
main() {
    local verbose=false
    local dry_run=false
    local silent=false

    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--verbose)
                verbose=true
                shift
                ;;
            -d|--dry-run)
                dry_run=true
                shift
                ;;
            -s|--silent)
                silent=true
                shift
                ;;
            *)
                log_error "未知参数: $1"
                log_info "使用 -h 或 --help 查看帮助信息"
                exit 1
                ;;
        esac
    done

    # 检查是否有权限
    if [ ! -w "/root/autodl-tmp/" ]; then
        log_error "没有写入权限: /root/autodl-tmp/"
        log_error "请确保以适当的权限运行此脚本"
        exit 1
    fi

    # 执行清理
    clean_cache "$verbose" "$dry_run" "$silent"
}

# 执行主函数
main "$@"
